(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// API Configuration and Client
__turbopack_context__.s({
    "apiClient": ()=>apiClient,
    "authApi": ()=>authApi
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
const API_BASE_URL = ("TURBOPACK compile-time value", "") || 'https://smart-ai-api.onrender.com/api/v1';
// Mock mode for development
const USE_MOCK_API = ("TURBOPACK compile-time value", "development") === 'development' || !("TURBOPACK compile-time value", "");
// Mock API responses for development
const MOCK_USERS = [
    {
        email: '<EMAIL>',
        password: 'admin123',
        user_id: 'admin-001',
        full_name: 'مدير النظام',
        is_staff: true,
        email_verified: true,
        phone_verified: true
    },
    {
        email: '<EMAIL>',
        password: 'user123',
        user_id: 'user-001',
        full_name: 'مستخدم تجريبي',
        is_staff: false,
        email_verified: true,
        phone_verified: false
    }
];
// Mock API functions
const mockApi = {
    async login (credentials) {
        // Simulate network delay
        await new Promise((resolve)=>setTimeout(resolve, 500));
        const user = MOCK_USERS.find((u)=>u.email === credentials.email && u.password === credentials.password);
        if (!user) {
            throw {
                message: 'بيانات تسجيل الدخول غير صحيحة',
                errors: {
                    email: [
                        'البريد الإلكتروني أو كلمة المرور غير صحيحة'
                    ]
                },
                status: 401
            };
        }
        return {
            access: 'mock-access-token-' + Date.now(),
            refresh: 'mock-refresh-token-' + Date.now(),
            user_id: user.user_id,
            email: user.email,
            full_name: user.full_name,
            is_staff: user.is_staff,
            email_verified: user.email_verified,
            phone_verified: user.phone_verified
        };
    },
    async register (userData) {
        // Simulate network delay
        await new Promise((resolve)=>setTimeout(resolve, 800));
        // Check if user already exists
        const existingUser = MOCK_USERS.find((u)=>u.email === userData.email);
        if (existingUser) {
            throw {
                message: 'المستخدم موجود بالفعل',
                errors: {
                    email: [
                        'هذا البريد الإلكتروني مستخدم بالفعل'
                    ]
                },
                status: 400
            };
        }
        // Check password confirmation
        if (userData.password !== userData.password_confirm) {
            throw {
                message: 'كلمات المرور غير متطابقة',
                errors: {
                    password_confirm: [
                        'كلمات المرور غير متطابقة'
                    ]
                },
                status: 400
            };
        }
        return {
            message: 'تم إنشاء الحساب بنجاح',
            user_id: 'user-' + Date.now(),
            email_verification_required: true,
            phone_verification_required: true
        };
    },
    async refreshToken (refreshToken) {
        // Simulate network delay
        await new Promise((resolve)=>setTimeout(resolve, 300));
        if (!refreshToken || !refreshToken.startsWith('mock-refresh-token')) {
            throw {
                message: 'رمز التحديث غير صالح',
                errors: {},
                status: 401
            };
        }
        return {
            access: 'mock-access-token-' + Date.now()
        };
    }
};
class ApiClient {
    async request(endpoint) {
        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        const url = "".concat(this.baseURL).concat(endpoint);
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };
        // Add auth token if available
        const token = this.getAccessToken();
        if (token) {
            config.headers = {
                ...config.headers,
                Authorization: "Bearer ".concat(token)
            };
        }
        console.log('API Request:', {
            url,
            method: config.method || 'GET',
            headers: config.headers
        });
        try {
            // Create AbortController for timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(()=>{
                controller.abort();
            }, 30000); // 30 seconds timeout
            // Add signal to config
            config.signal = controller.signal;
            const response = await fetch(url, config);
            clearTimeout(timeoutId);
            console.log('API Response:', {
                status: response.status,
                statusText: response.statusText
            });
            if (!response.ok) {
                const errorData = await response.json().catch(()=>({}));
                console.error('API Error:', errorData);
                throw {
                    message: errorData.message || errorData.detail || "HTTP ".concat(response.status, ": ").concat(response.statusText),
                    errors: errorData.errors || {},
                    status: response.status
                };
            }
            const data = await response.json();
            console.log('API Success:', data);
            return data;
        } catch (error) {
            console.error('API Request Failed:', error);
            if (error instanceof Error) {
                // Handle different types of errors
                if (error.name === 'AbortError') {
                    throw {
                        message: 'انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.',
                        status: 0
                    };
                }
                if (error.message.includes('fetch') || error.message.includes('NetworkError')) {
                    throw {
                        message: 'فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.',
                        status: 0
                    };
                }
                throw {
                    message: error.message,
                    status: 0
                };
            }
            throw error;
        }
    }
    // Authentication methods
    async login(credentials) {
        // Use mock API in development mode or when API is not available
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('Using mock API for login');
            try {
                const response = await mockApi.login(credentials);
                // Store tokens
                this.setTokens(response.access, response.refresh);
                this.setUserData(response);
                return response;
            } catch (error) {
                console.error('Mock API login error:', error);
                throw error;
            }
        }
        try {
            const response = await this.request('/auth/login/', {
                method: 'POST',
                body: JSON.stringify(credentials)
            });
            // Store tokens
            this.setTokens(response.access, response.refresh);
            this.setUserData(response);
            return response;
        } catch (error) {
            // Fallback to mock API if real API fails
            console.log('Real API failed, falling back to mock API');
            try {
                const response = await mockApi.login(credentials);
                // Store tokens
                this.setTokens(response.access, response.refresh);
                this.setUserData(response);
                return response;
            } catch (mockError) {
                console.error('Mock API also failed:', mockError);
                throw mockError;
            }
        }
    }
    async register(userData) {
        // Use mock API in development mode or when API is not available
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('Using mock API for register');
            return mockApi.register(userData);
        }
        //TURBOPACK unreachable
        ;
    }
    async logout() {
        const refreshToken = this.getRefreshToken();
        if (refreshToken) {
            try {
                await this.request('/auth/logout/', {
                    method: 'POST',
                    body: JSON.stringify({
                        refresh: refreshToken
                    })
                });
            } catch (error) {
                console.error('Logout error:', error);
            }
        }
        this.clearTokens();
    }
    async refreshToken() {
        const refreshToken = this.getRefreshToken();
        if (!refreshToken) {
            throw new Error('No refresh token available');
        }
        // Use mock API in development mode or when API is not available
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('Using mock API for token refresh');
            const response = await mockApi.refreshToken(refreshToken);
            this.setTokens(response.access, refreshToken); // Keep the same refresh token
            // Return a full LoginResponse (mock doesn't return user data, so we'll use stored data)
            const userData = this.getUserData();
            return {
                access: response.access,
                refresh: refreshToken,
                user_id: (userData === null || userData === void 0 ? void 0 : userData.user_id) || 'mock-user',
                email: (userData === null || userData === void 0 ? void 0 : userData.email) || '<EMAIL>',
                full_name: (userData === null || userData === void 0 ? void 0 : userData.full_name) || 'مدير النظام',
                is_staff: (userData === null || userData === void 0 ? void 0 : userData.is_staff) || true,
                email_verified: (userData === null || userData === void 0 ? void 0 : userData.email_verified) || true,
                phone_verified: (userData === null || userData === void 0 ? void 0 : userData.phone_verified) || false
            };
        }
        //TURBOPACK unreachable
        ;
    }
    async resetPassword(email) {
        return this.request('/auth/password/reset/request/', {
            method: 'POST',
            body: JSON.stringify({
                email
            })
        });
    }
    async confirmPasswordReset(token, newPassword) {
        return this.request('/auth/password/reset/confirm/', {
            method: 'POST',
            body: JSON.stringify({
                token,
                new_password: newPassword
            })
        });
    }
    async changePassword(oldPassword, newPassword) {
        return this.request('/auth/password/change/', {
            method: 'POST',
            body: JSON.stringify({
                old_password: oldPassword,
                new_password: newPassword
            })
        });
    }
    // Token management
    setTokens(accessToken, refreshToken) {
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.setItem('access_token', accessToken);
            localStorage.setItem('refresh_token', refreshToken);
        }
    }
    getAccessToken() {
        if ("TURBOPACK compile-time truthy", 1) {
            return localStorage.getItem('access_token');
        }
        //TURBOPACK unreachable
        ;
    }
    getRefreshToken() {
        if ("TURBOPACK compile-time truthy", 1) {
            return localStorage.getItem('refresh_token');
        }
        //TURBOPACK unreachable
        ;
    }
    clearTokens() {
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_data');
        }
    }
    // User data management
    setUserData(userData) {
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.setItem('user_data', JSON.stringify(userData));
        }
    }
    getUserData() {
        if ("TURBOPACK compile-time truthy", 1) {
            const userData = localStorage.getItem('user_data');
            return userData ? JSON.parse(userData) : null;
        }
        //TURBOPACK unreachable
        ;
    }
    isAuthenticated() {
        return !!this.getAccessToken();
    }
    isAdmin() {
        const userData = this.getUserData();
        return (userData === null || userData === void 0 ? void 0 : userData.is_staff) || false;
    }
    constructor(baseURL = API_BASE_URL){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "baseURL", void 0);
        this.baseURL = baseURL;
    }
}
const apiClient = new ApiClient();
const authApi = {
    login: (credentials)=>apiClient.login(credentials),
    register: (userData)=>apiClient.register(userData),
    logout: ()=>apiClient.logout(),
    refreshToken: ()=>apiClient.refreshToken(),
    resetPassword: (email)=>apiClient.resetPassword(email),
    confirmPasswordReset: (token, newPassword)=>apiClient.confirmPasswordReset(token, newPassword),
    changePassword: (oldPassword, newPassword)=>apiClient.changePassword(oldPassword, newPassword),
    isAuthenticated: ()=>apiClient.isAuthenticated(),
    isAdmin: ()=>apiClient.isAdmin(),
    getUserData: ()=>apiClient.getUserData(),
    setUserData: (userData)=>apiClient.setUserData(userData)
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/auth.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAuth": ()=>useAuth,
    "useAuthStore": ()=>useAuthStore
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
;
;
const useAuthStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // Initial state
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        // Login action
        login: async (credentials)=>{
            set({
                isLoading: true,
                error: null
            });
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].login(credentials);
                const user = {
                    user_id: response.user_id,
                    email: response.email,
                    full_name: response.full_name,
                    is_staff: response.is_staff,
                    email_verified: response.email_verified,
                    phone_verified: response.phone_verified
                };
                // Store user data in API client
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].setUserData(response);
                set({
                    user,
                    isAuthenticated: true,
                    isLoading: false,
                    error: null
                });
                console.log('Login successful, user data set:', user);
                console.log('isAuthenticated set to:', true);
                // إزالة رسالة الترحيب لتجنب ظهورها
                // toast.success(`مرحباً ${user.full_name}!`);
                // إزالة التأخير لجعل العملية أسرع
                // await new Promise(resolve => setTimeout(resolve, 100));
                return true;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'فشل في تسجيل الدخول';
                set({
                    user: null,
                    isAuthenticated: false,
                    isLoading: false,
                    error: errorMessage
                });
                console.error('Login failed:', errorMessage);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].error(errorMessage);
                return false;
            }
        },
        // Register action
        register: async (userData)=>{
            set({
                isLoading: true,
                error: null
            });
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].register(userData);
                set({
                    isLoading: false,
                    error: null
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].success('تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني.');
                return true;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'فشل في إنشاء الحساب';
                set({
                    isLoading: false,
                    error: errorMessage
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].error(errorMessage);
                return false;
            }
        },
        // Logout action
        logout: async ()=>{
            set({
                isLoading: true
            });
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].logout();
            } catch (error) {
                console.error('Logout error:', error);
            } finally{
                set({
                    user: null,
                    isAuthenticated: false,
                    isLoading: false,
                    error: null
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].success('تم تسجيل الخروج بنجاح');
            }
        },
        // Clear error
        clearError: ()=>{
            set({
                error: null
            });
        },
        // Check authentication status
        checkAuth: async ()=>{
            const isAuth = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].isAuthenticated();
            const userData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].getUserData();
            if (isAuth && userData) {
                const user = {
                    user_id: userData.user_id || '',
                    email: userData.email || '',
                    full_name: userData.full_name || '',
                    is_staff: userData.is_staff || false,
                    email_verified: userData.email_verified || false,
                    phone_verified: userData.phone_verified || false
                };
                set({
                    user,
                    isAuthenticated: true
                });
            } else {
                set({
                    user: null,
                    isAuthenticated: false
                });
            }
        },
        // Refresh authentication
        refreshAuth: async ()=>{
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].refreshToken();
                const user = {
                    user_id: response.user_id,
                    email: response.email,
                    full_name: response.full_name,
                    is_staff: response.is_staff,
                    email_verified: response.email_verified,
                    phone_verified: response.phone_verified
                };
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].setUserData(response);
                set({
                    user,
                    isAuthenticated: true,
                    error: null
                });
                return true;
            } catch (e) {
                // If refresh fails, logout user
                get().logout();
                return false;
            }
        }
    }), {
    name: 'auth-storage',
    partialize: (state)=>({
            user: state.user,
            isAuthenticated: state.isAuthenticated
        })
}));
const useAuth = ()=>{
    var _store_user;
    _s();
    const store = useAuthStore();
    return {
        user: store.user,
        isAuthenticated: store.isAuthenticated,
        isLoading: store.isLoading,
        error: store.error,
        isAdmin: ((_store_user = store.user) === null || _store_user === void 0 ? void 0 : _store_user.is_staff) || false,
        login: store.login,
        register: store.register,
        logout: store.logout,
        clearError: store.clearError,
        checkAuth: store.checkAuth,
        refreshAuth: store.refreshAuth
    };
};
_s(useAuth, "8ZC44qhbtMxv0wotksQALRPDM9c=", false, function() {
    return [
        useAuthStore
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/auth-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": ()=>AuthProvider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/auth.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function AuthProvider(param) {
    let { children } = param;
    _s();
    const { checkAuth } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            // Check authentication status on app load
            checkAuth();
        }
    }["AuthProvider.useEffect"], [
        checkAuth
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(AuthProvider, "IRyJx12P9TT/VkKVGJylPp/kH2Y=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = AuthProvider;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_afdf7ce4._.js.map