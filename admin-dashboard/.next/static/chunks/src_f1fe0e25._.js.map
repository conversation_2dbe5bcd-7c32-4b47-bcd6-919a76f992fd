{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/admin/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/stores/auth';\nimport { Button } from '@/components/ui/button';\nimport { LogOut, User } from 'lucide-react';\n\nexport default function DashboardPage() {\n  const { user, logout } = useAuth();\n\n  const handleLogout = async () => {\n    await logout();\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Dashboard</h1>\n          <p className=\"text-gray-600\">\n            مرحباً {user?.full_name || 'المستخدم'} - مرحباً بك في لوحة التحكم الإدارية\n          </p>\n        </div>\n        <div className=\"flex items-center gap-4\">\n          <div className=\"flex items-center gap-2 text-sm text-gray-600\">\n            <User className=\"h-4 w-4\" />\n            <span>{user?.email}</span>\n          </div>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleLogout}\n            className=\"flex items-center gap-2\"\n          >\n            <LogOut className=\"h-4 w-4\" />\n            تسجيل الخروج\n          </Button>\n        </div>\n      </div>\n      \n      {/* Placeholder for dashboard content */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n          <h3 className=\"text-sm font-medium text-gray-500\">Total Sales</h3>\n          <p className=\"text-2xl font-bold text-gray-900\">$12,345</p>\n        </div>\n        \n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n          <h3 className=\"text-sm font-medium text-gray-500\">Orders</h3>\n          <p className=\"text-2xl font-bold text-gray-900\">123</p>\n        </div>\n        \n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n          <h3 className=\"text-sm font-medium text-gray-500\">Customers</h3>\n          <p className=\"text-2xl font-bold text-gray-900\">456</p>\n        </div>\n        \n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n          <h3 className=\"text-sm font-medium text-gray-500\">Products</h3>\n          <p className=\"text-2xl font-bold text-gray-900\">789</p>\n        </div>\n      </div>\n      \n      <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n          Recent Activity\n        </h2>\n        <p className=\"text-gray-600\">\n          Dashboard content will be implemented in Phase 5\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAE/B,MAAM,eAAe;QACnB,MAAM;IACR;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;;oCAAgB;oCACnB,CAAA,iBAAA,2BAAA,KAAM,SAAS,KAAI;oCAAW;;;;;;;;;;;;;kCAG1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAM,iBAAA,2BAAA,KAAM,KAAK;;;;;;;;;;;;0CAEpB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;0BAOpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAGlD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAGlD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAGlD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;0BAIpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAMrC;GAlEwB;;QACG,wHAAA,CAAA,UAAO;;;KADV", "debugId": null}}]}