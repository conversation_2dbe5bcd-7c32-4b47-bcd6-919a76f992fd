{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,iKAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,6JAAA,CAAA,gBAAmB,CAC1C,CAAC;AAGH,MAAM,YAAY;QAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6LAAC,iKAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,6JAAA,CAAA,aAAgB,CAAC;IACtC,MAAM,cAAc,6JAAA,CAAA,aAAgB,CAAC;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,AAAC,GAAK,OAAH,IAAG;QAClB,mBAAmB,AAAC,GAAK,OAAH,IAAG;QACzB,eAAe,AAAC,GAAK,OAAH,IAAG;QACrB,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,iKAAA,CAAA,iBAAc;QACtB,iKAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,6JAAA,CAAA,gBAAmB,CACzC,CAAC;AAGH,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;;IAChB,MAAM,KAAK,6JAAA,CAAA,QAAW;IAEtB,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,KAGgC;QAHhC,EACjB,SAAS,EACT,GAAG,OAC8C,GAHhC;;IAIjB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,KAA+C;QAA/C,EAAE,GAAG,OAA0C,GAA/C;;IACnB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6LAAC,mKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,AAAC,GAAoB,OAAlB,qBACH,AAAC,GAAuB,OAArB,mBAAkB,KAAiB,OAAd;QAE9B,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAhBS;;QACyD;;;MADzD;AAkBT,SAAS,gBAAgB,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACvB,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACnB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;QACL;IAA5B,MAAM,OAAO,QAAQ,OAAO,CAAA,iBAAA,kBAAA,4BAAA,MAAO,OAAO,cAAd,4BAAA,iBAAkB,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/lib/api.ts"], "sourcesContent": ["// API Configuration and Client\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://smart-ai-api.onrender.com/api/v1';\n\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  access: string;\n  refresh: string;\n  user_id: string;\n  email: string;\n  full_name: string;\n  is_staff: boolean;\n  email_verified: boolean;\n  phone_verified: boolean;\n}\n\nexport interface RegisterRequest {\n  email: string;\n  password: string;\n  password_confirm: string;\n  full_name: string;\n  university_id?: string;\n}\n\nexport interface RegisterResponse {\n  message: string;\n  user_id: string;\n  email_verification_required: boolean;\n  phone_verification_required: boolean;\n}\n\nexport interface ApiError {\n  message: string;\n  errors?: Record<string, string[]>;\n  status: number;\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseURL}${endpoint}`;\n    \n    const config: RequestInit = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    // Add auth token if available\n    const token = this.getAccessToken();\n    if (token) {\n      config.headers = {\n        ...config.headers,\n        Authorization: `Bearer ${token}`,\n      };\n    }\n\n    try {\n      const response = await fetch(url, config);\n      \n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw {\n          message: errorData.message || errorData.detail || 'An error occurred',\n          errors: errorData.errors || {},\n          status: response.status,\n        } as ApiError;\n      }\n\n      return await response.json();\n    } catch (error) {\n      if (error instanceof Error) {\n        throw {\n          message: error.message,\n          status: 0,\n        } as ApiError;\n      }\n      throw error;\n    }\n  }\n\n  // Authentication methods\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    const response = await this.request<LoginResponse>('/auth/login/', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    });\n    \n    // Store tokens\n    this.setTokens(response.access, response.refresh);\n    \n    return response;\n  }\n\n  async register(userData: RegisterRequest): Promise<RegisterResponse> {\n    return this.request<RegisterResponse>('/auth/register/', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    });\n  }\n\n  async logout(): Promise<void> {\n    const refreshToken = this.getRefreshToken();\n    if (refreshToken) {\n      try {\n        await this.request('/auth/logout/', {\n          method: 'POST',\n          body: JSON.stringify({ refresh: refreshToken }),\n        });\n      } catch (error) {\n        console.error('Logout error:', error);\n      }\n    }\n    \n    this.clearTokens();\n  }\n\n  async refreshToken(): Promise<LoginResponse> {\n    const refreshToken = this.getRefreshToken();\n    if (!refreshToken) {\n      throw new Error('No refresh token available');\n    }\n\n    const response = await this.request<LoginResponse>('/auth/token/refresh/', {\n      method: 'POST',\n      body: JSON.stringify({ refresh: refreshToken }),\n    });\n\n    this.setTokens(response.access, response.refresh);\n    return response;\n  }\n\n  async resetPassword(email: string): Promise<{ message: string }> {\n    return this.request('/auth/password/reset/request/', {\n      method: 'POST',\n      body: JSON.stringify({ email }),\n    });\n  }\n\n  async confirmPasswordReset(token: string, newPassword: string): Promise<{ message: string }> {\n    return this.request('/auth/password/reset/confirm/', {\n      method: 'POST',\n      body: JSON.stringify({ token, new_password: newPassword }),\n    });\n  }\n\n  async changePassword(oldPassword: string, newPassword: string): Promise<{ message: string }> {\n    return this.request('/auth/password/change/', {\n      method: 'POST',\n      body: JSON.stringify({ old_password: oldPassword, new_password: newPassword }),\n    });\n  }\n\n  // Token management\n  private setTokens(accessToken: string, refreshToken: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('access_token', accessToken);\n      localStorage.setItem('refresh_token', refreshToken);\n    }\n  }\n\n  private getAccessToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('access_token');\n    }\n    return null;\n  }\n\n  private getRefreshToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('refresh_token');\n    }\n    return null;\n  }\n\n  private clearTokens(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('refresh_token');\n      localStorage.removeItem('user_data');\n    }\n  }\n\n  // User data management\n  setUserData(userData: Partial<LoginResponse>): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('user_data', JSON.stringify(userData));\n    }\n  }\n\n  getUserData(): Partial<LoginResponse> | null {\n    if (typeof window !== 'undefined') {\n      const userData = localStorage.getItem('user_data');\n      return userData ? JSON.parse(userData) : null;\n    }\n    return null;\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.getAccessToken();\n  }\n\n  isAdmin(): boolean {\n    const userData = this.getUserData();\n    return userData?.is_staff || false;\n  }\n}\n\n// Export singleton instance\nexport const apiClient = new ApiClient();\n\n// Export individual auth functions for convenience\nexport const authApi = {\n  login: (credentials: LoginRequest) => apiClient.login(credentials),\n  register: (userData: RegisterRequest) => apiClient.register(userData),\n  logout: () => apiClient.logout(),\n  refreshToken: () => apiClient.refreshToken(),\n  resetPassword: (email: string) => apiClient.resetPassword(email),\n  confirmPasswordReset: (token: string, newPassword: string) => \n    apiClient.confirmPasswordReset(token, newPassword),\n  changePassword: (oldPassword: string, newPassword: string) => \n    apiClient.changePassword(oldPassword, newPassword),\n  isAuthenticated: () => apiClient.isAuthenticated(),\n  isAdmin: () => apiClient.isAdmin(),\n  getUserData: () => apiClient.getUserData(),\n  setUserData: (userData: Partial<LoginResponse>) => apiClient.setUserData(userData),\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;AACV;;;AAArB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI;AAuC7D,MAAM;IAOJ,MAAc,QACZ,QAAgB,EAEJ;YADZ,UAAA,iEAAuB,CAAC;QAExB,MAAM,MAAM,AAAC,GAAiB,OAAf,IAAI,CAAC,OAAO,EAAY,OAAT;QAE9B,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,8BAA8B;QAC9B,MAAM,QAAQ,IAAI,CAAC,cAAc;QACjC,IAAI,OAAO;YACT,OAAO,OAAO,GAAG;gBACf,GAAG,OAAO,OAAO;gBACjB,eAAe,AAAC,UAAe,OAAN;YAC3B;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM;oBACJ,SAAS,UAAU,OAAO,IAAI,UAAU,MAAM,IAAI;oBAClD,QAAQ,UAAU,MAAM,IAAI,CAAC;oBAC7B,QAAQ,SAAS,MAAM;gBACzB;YACF;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,OAAO;gBAC1B,MAAM;oBACJ,SAAS,MAAM,OAAO;oBACtB,QAAQ;gBACV;YACF;YACA,MAAM;QACR;IACF;IAEA,yBAAyB;IACzB,MAAM,MAAM,WAAyB,EAA0B;QAC7D,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAgB,gBAAgB;YACjE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,eAAe;QACf,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,EAAE,SAAS,OAAO;QAEhD,OAAO;IACT;IAEA,MAAM,SAAS,QAAyB,EAA6B;QACnE,OAAO,IAAI,CAAC,OAAO,CAAmB,mBAAmB;YACvD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,SAAwB;QAC5B,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,IAAI,cAAc;YAChB,IAAI;gBACF,MAAM,IAAI,CAAC,OAAO,CAAC,iBAAiB;oBAClC,QAAQ;oBACR,MAAM,KAAK,SAAS,CAAC;wBAAE,SAAS;oBAAa;gBAC/C;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC;QACF;QAEA,IAAI,CAAC,WAAW;IAClB;IAEA,MAAM,eAAuC;QAC3C,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAgB,wBAAwB;YACzE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,SAAS;YAAa;QAC/C;QAEA,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,EAAE,SAAS,OAAO;QAChD,OAAO;IACT;IAEA,MAAM,cAAc,KAAa,EAAgC;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAC,iCAAiC;YACnD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;IACF;IAEA,MAAM,qBAAqB,KAAa,EAAE,WAAmB,EAAgC;QAC3F,OAAO,IAAI,CAAC,OAAO,CAAC,iCAAiC;YACnD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO,cAAc;YAAY;QAC1D;IACF;IAEA,MAAM,eAAe,WAAmB,EAAE,WAAmB,EAAgC;QAC3F,OAAO,IAAI,CAAC,OAAO,CAAC,0BAA0B;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,cAAc;gBAAa,cAAc;YAAY;QAC9E;IACF;IAEA,mBAAmB;IACX,UAAU,WAAmB,EAAE,YAAoB,EAAQ;QACjE,wCAAmC;YACjC,aAAa,OAAO,CAAC,gBAAgB;YACrC,aAAa,OAAO,CAAC,iBAAiB;QACxC;IACF;IAEQ,iBAAgC;QACtC,wCAAmC;YACjC,OAAO,aAAa,OAAO,CAAC;QAC9B;;;IAEF;IAEQ,kBAAiC;QACvC,wCAAmC;YACjC,OAAO,aAAa,OAAO,CAAC;QAC9B;;;IAEF;IAEQ,cAAoB;QAC1B,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,uBAAuB;IACvB,YAAY,QAAgC,EAAQ;QAClD,wCAAmC;YACjC,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACnD;IACF;IAEA,cAA6C;QAC3C,wCAAmC;YACjC,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,OAAO,WAAW,KAAK,KAAK,CAAC,YAAY;QAC3C;;;IAEF;IAEA,kBAA2B;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc;IAC9B;IAEA,UAAmB;QACjB,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,OAAO,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;IAC/B;IA/KA,YAAY,UAAkB,YAAY,CAAE;QAF5C,+KAAQ,WAAR,KAAA;QAGE,IAAI,CAAC,OAAO,GAAG;IACjB;AA8KF;AAGO,MAAM,YAAY,IAAI;AAGtB,MAAM,UAAU;IACrB,OAAO,CAAC,cAA8B,UAAU,KAAK,CAAC;IACtD,UAAU,CAAC,WAA8B,UAAU,QAAQ,CAAC;IAC5D,QAAQ,IAAM,UAAU,MAAM;IAC9B,cAAc,IAAM,UAAU,YAAY;IAC1C,eAAe,CAAC,QAAkB,UAAU,aAAa,CAAC;IAC1D,sBAAsB,CAAC,OAAe,cACpC,UAAU,oBAAoB,CAAC,OAAO;IACxC,gBAAgB,CAAC,aAAqB,cACpC,UAAU,cAAc,CAAC,aAAa;IACxC,iBAAiB,IAAM,UAAU,eAAe;IAChD,SAAS,IAAM,UAAU,OAAO;IAChC,aAAa,IAAM,UAAU,WAAW;IACxC,aAAa,CAAC,WAAqC,UAAU,WAAW,CAAC;AAC3E", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/stores/auth.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { authApi, LoginRequest, LoginResponse, RegisterRequest } from '@/lib/api';\nimport toast from 'react-hot-toast';\n\ninterface User {\n  user_id: string;\n  email: string;\n  full_name: string;\n  is_staff: boolean;\n  email_verified: boolean;\n  phone_verified: boolean;\n}\n\ninterface AuthState {\n  // State\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n\n  // Actions\n  login: (credentials: LoginRequest) => Promise<boolean>;\n  register: (userData: RegisterRequest) => Promise<boolean>;\n  logout: () => Promise<void>;\n  clearError: () => void;\n  checkAuth: () => void;\n  refreshAuth: () => Promise<boolean>;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // Login action\n      login: async (credentials: LoginRequest): Promise<boolean> => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const response = await authApi.login(credentials);\n          \n          const user: User = {\n            user_id: response.user_id,\n            email: response.email,\n            full_name: response.full_name,\n            is_staff: response.is_staff,\n            email_verified: response.email_verified,\n            phone_verified: response.phone_verified,\n          };\n\n          // Store user data in API client\n          authApi.setUserData(response);\n\n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n\n          toast.success(`مرحباً ${user.full_name}!`);\n          return true;\n        } catch (error: any) {\n          const errorMessage = error.message || 'فشل في تسجيل الدخول';\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          toast.error(errorMessage);\n          return false;\n        }\n      },\n\n      // Register action\n      register: async (userData: RegisterRequest): Promise<boolean> => {\n        set({ isLoading: true, error: null });\n\n        try {\n          await authApi.register(userData);\n          \n          set({\n            isLoading: false,\n            error: null,\n          });\n\n          toast.success('تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني.');\n          return true;\n        } catch (error: any) {\n          const errorMessage = error.message || 'فشل في إنشاء الحساب';\n          set({\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          toast.error(errorMessage);\n          return false;\n        }\n      },\n\n      // Logout action\n      logout: async (): Promise<void> => {\n        set({ isLoading: true });\n\n        try {\n          await authApi.logout();\n        } catch (error) {\n          console.error('Logout error:', error);\n        } finally {\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n\n          toast.success('تم تسجيل الخروج بنجاح');\n        }\n      },\n\n      // Clear error\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // Check authentication status\n      checkAuth: () => {\n        const isAuth = authApi.isAuthenticated();\n        const userData = authApi.getUserData();\n\n        if (isAuth && userData) {\n          const user: User = {\n            user_id: userData.user_id || '',\n            email: userData.email || '',\n            full_name: userData.full_name || '',\n            is_staff: userData.is_staff || false,\n            email_verified: userData.email_verified || false,\n            phone_verified: userData.phone_verified || false,\n          };\n\n          set({\n            user,\n            isAuthenticated: true,\n          });\n        } else {\n          set({\n            user: null,\n            isAuthenticated: false,\n          });\n        }\n      },\n\n      // Refresh authentication\n      refreshAuth: async (): Promise<boolean> => {\n        try {\n          const response = await authApi.refreshToken();\n          \n          const user: User = {\n            user_id: response.user_id,\n            email: response.email,\n            full_name: response.full_name,\n            is_staff: response.is_staff,\n            email_verified: response.email_verified,\n            phone_verified: response.phone_verified,\n          };\n\n          authApi.setUserData(response);\n\n          set({\n            user,\n            isAuthenticated: true,\n            error: null,\n          });\n\n          return true;\n        } catch (error) {\n          // If refresh fails, logout user\n          get().logout();\n          return false;\n        }\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// Helper hooks\nexport const useAuth = () => {\n  const store = useAuthStore();\n  return {\n    user: store.user,\n    isAuthenticated: store.isAuthenticated,\n    isLoading: store.isLoading,\n    error: store.error,\n    isAdmin: store.user?.is_staff || false,\n    login: store.login,\n    register: store.register,\n    logout: store.logout,\n    clearError: store.clearError,\n    checkAuth: store.checkAuth,\n    refreshAuth: store.refreshAuth,\n  };\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AA2BO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,eAAe;QACf,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAErC,MAAM,OAAa;oBACjB,SAAS,SAAS,OAAO;oBACzB,OAAO,SAAS,KAAK;oBACrB,WAAW,SAAS,SAAS;oBAC7B,UAAU,SAAS,QAAQ;oBAC3B,gBAAgB,SAAS,cAAc;oBACvC,gBAAgB,SAAS,cAAc;gBACzC;gBAEA,gCAAgC;gBAChC,oHAAA,CAAA,UAAO,CAAC,WAAW,CAAC;gBAEpB,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,AAAC,UAAwB,OAAf,KAAK,SAAS,EAAC;gBACvC,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,OAAO,IAAI;gBACtC,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,kBAAkB;QAClB,UAAU,OAAO;YACf,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,oHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;gBAEvB,IAAI;oBACF,WAAW;oBACX,OAAO;gBACT;gBAEA,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,OAAO,IAAI;gBACtC,IAAI;oBACF,WAAW;oBACX,OAAO;gBACT;gBAEA,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,gBAAgB;QAChB,QAAQ;YACN,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC,SAAU;gBACR,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,cAAc;QACd,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,8BAA8B;QAC9B,WAAW;YACT,MAAM,SAAS,oHAAA,CAAA,UAAO,CAAC,eAAe;YACtC,MAAM,WAAW,oHAAA,CAAA,UAAO,CAAC,WAAW;YAEpC,IAAI,UAAU,UAAU;gBACtB,MAAM,OAAa;oBACjB,SAAS,SAAS,OAAO,IAAI;oBAC7B,OAAO,SAAS,KAAK,IAAI;oBACzB,WAAW,SAAS,SAAS,IAAI;oBACjC,UAAU,SAAS,QAAQ,IAAI;oBAC/B,gBAAgB,SAAS,cAAc,IAAI;oBAC3C,gBAAgB,SAAS,cAAc,IAAI;gBAC7C;gBAEA,IAAI;oBACF;oBACA,iBAAiB;gBACnB;YACF,OAAO;gBACL,IAAI;oBACF,MAAM;oBACN,iBAAiB;gBACnB;YACF;QACF;QAEA,yBAAyB;QACzB,aAAa;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,YAAY;gBAE3C,MAAM,OAAa;oBACjB,SAAS,SAAS,OAAO;oBACzB,OAAO,SAAS,KAAK;oBACrB,WAAW,SAAS,SAAS;oBAC7B,UAAU,SAAS,QAAQ;oBAC3B,gBAAgB,SAAS,cAAc;oBACvC,gBAAgB,SAAS,cAAc;gBACzC;gBAEA,oHAAA,CAAA,UAAO,CAAC,WAAW,CAAC;gBAEpB,IAAI;oBACF;oBACA,iBAAiB;oBACjB,OAAO;gBACT;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,gCAAgC;gBAChC,MAAM,MAAM;gBACZ,OAAO;YACT;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AAKG,MAAM,UAAU;QAOV;;IANX,MAAM,QAAQ;IACd,OAAO;QACL,MAAM,MAAM,IAAI;QAChB,iBAAiB,MAAM,eAAe;QACtC,WAAW,MAAM,SAAS;QAC1B,OAAO,MAAM,KAAK;QAClB,SAAS,EAAA,cAAA,MAAM,IAAI,cAAV,kCAAA,YAAY,QAAQ,KAAI;QACjC,OAAO,MAAM,KAAK;QAClB,UAAU,MAAM,QAAQ;QACxB,QAAQ,MAAM,MAAM;QACpB,YAAY,MAAM,UAAU;QAC5B,WAAW,MAAM,SAAS;QAC1B,aAAa,MAAM,WAAW;IAChC;AACF;GAfa;;QACG", "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport Link from 'next/link';\nimport { Eye, EyeOff, Lock, Mail, LogIn } from 'lucide-react';\n\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\n\nimport { useAuth } from '@/stores/auth';\nimport { LoginRequest } from '@/lib/api';\n\n// Validation schema\nconst loginSchema = z.object({\n  email: z\n    .string()\n    .min(1, 'البريد الإلكتروني مطلوب')\n    .email('يرجى إدخال بريد إلكتروني صحيح'),\n  password: z\n    .string()\n    .min(1, 'كلمة المرور مطلوبة')\n    .min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),\n});\n\ntype LoginFormData = z.infer<typeof loginSchema>;\n\nexport default function LoginPage() {\n  const router = useRouter();\n  const { login, isLoading, error, isAuthenticated, clearError } = useAuth();\n  const [showPassword, setShowPassword] = useState(false);\n\n  const form = useForm<LoginFormData>({\n    resolver: zodResolver(loginSchema),\n    defaultValues: {\n      email: '',\n      password: '',\n    },\n  });\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/admin/dashboard');\n    }\n  }, [isAuthenticated, router]);\n\n  // Clear error when component unmounts or form changes\n  useEffect(() => {\n    return () => clearError();\n  }, [clearError]);\n\n  const onSubmit = async (data: LoginFormData) => {\n    clearError();\n    \n    const credentials: LoginRequest = {\n      email: data.email,\n      password: data.password,\n    };\n\n    const success = await login(credentials);\n    \n    if (success) {\n      router.push('/admin/dashboard');\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          <div className=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100\">\n            <Lock className=\"h-6 w-6 text-blue-600\" />\n          </div>\n          <h2 className=\"mt-6 text-3xl font-bold text-gray-900\">\n            تسجيل الدخول\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            ادخل إلى لوحة التحكم الإدارية\n          </p>\n        </div>\n\n        <Card>\n          <CardHeader className=\"space-y-1\">\n            <CardTitle className=\"text-2xl text-center\">مرحباً بك</CardTitle>\n            <CardDescription className=\"text-center\">\n              يرجى إدخال بياناتك لتسجيل الدخول\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <Form {...form}>\n              <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n                {/* Email Field */}\n                <FormField\n                  control={form.control}\n                  name=\"email\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"flex items-center gap-2\">\n                        <Mail className=\"h-4 w-4\" />\n                        البريد الإلكتروني\n                      </FormLabel>\n                      <FormControl>\n                        <Input\n                          {...field}\n                          type=\"email\"\n                          placeholder=\"<EMAIL>\"\n                          disabled={isLoading}\n                          className=\"text-right\"\n                          dir=\"ltr\"\n                        />\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                {/* Password Field */}\n                <FormField\n                  control={form.control}\n                  name=\"password\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel className=\"flex items-center gap-2\">\n                        <Lock className=\"h-4 w-4\" />\n                        كلمة المرور\n                      </FormLabel>\n                      <FormControl>\n                        <div className=\"relative\">\n                          <Input\n                            {...field}\n                            type={showPassword ? 'text' : 'password'}\n                            placeholder=\"••••••••\"\n                            disabled={isLoading}\n                            className=\"pr-10\"\n                          />\n                          <button\n                            type=\"button\"\n                            onClick={togglePasswordVisibility}\n                            className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                            disabled={isLoading}\n                          >\n                            {showPassword ? (\n                              <EyeOff className=\"h-4 w-4 text-gray-400\" />\n                            ) : (\n                              <Eye className=\"h-4 w-4 text-gray-400\" />\n                            )}\n                          </button>\n                        </div>\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                {/* Error Message */}\n                {error && (\n                  <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n                    <p className=\"text-sm text-red-600 text-center\">{error}</p>\n                  </div>\n                )}\n\n                {/* Submit Button */}\n                <Button\n                  type=\"submit\"\n                  className=\"w-full\"\n                  disabled={isLoading}\n                >\n                  {isLoading ? (\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent\" />\n                      جاري تسجيل الدخول...\n                    </div>\n                  ) : (\n                    <div className=\"flex items-center gap-2\">\n                      <LogIn className=\"h-4 w-4\" />\n                      تسجيل الدخول\n                    </div>\n                  )}\n                </Button>\n\n                {/* Demo Credentials */}\n                <div className=\"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md\">\n                  <p className=\"text-xs text-blue-600 text-center font-medium mb-2\">\n                    بيانات تجريبية:\n                  </p>\n                  <div className=\"text-xs text-blue-600 space-y-1\">\n                    <p>البريد: <EMAIL></p>\n                    <p>كلمة المرور: admin123</p>\n                  </div>\n                </div>\n\n                {/* Links */}\n                <div className=\"text-center space-y-2\">\n                  <Link\n                    href=\"/forgot-password\"\n                    className=\"text-sm text-blue-600 hover:text-blue-500\"\n                  >\n                    نسيت كلمة المرور؟\n                  </Link>\n                  <div className=\"text-sm text-gray-600\">\n                    ليس لديك حساب؟{' '}\n                    <Link\n                      href=\"/register\"\n                      className=\"text-blue-600 hover:text-blue-500 font-medium\"\n                    >\n                      إنشاء حساب جديد\n                    </Link>\n                  </div>\n                </div>\n              </form>\n            </Form>\n          </CardContent>\n        </Card>\n\n        {/* Footer */}\n        <div className=\"text-center\">\n          <p className=\"text-xs text-gray-500\">\n            © 2025 لوحة التحكم الإدارية. جميع الحقوق محفوظة.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAEA;AACA;AAEA;;;AAhBA;;;;;;;;;;;;;AAmBA,oBAAoB;AACpB,MAAM,cAAc,kJAAA,CAAA,SAAQ,CAAC;IAC3B,OAAO,kJAAA,CAAA,SACE,GACN,GAAG,CAAC,GAAG,2BACP,KAAK,CAAC;IACT,UAAU,kJAAA,CAAA,SACD,GACN,GAAG,CAAC,GAAG,sBACP,GAAG,CAAC,GAAG;AACZ;AAIe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,eAAe,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAiB;QAClC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;YACP,UAAU;QACZ;IACF;IAEA,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC;QAAiB;KAAO;IAE5B,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;uCAAO,IAAM;;QACf;8BAAG;QAAC;KAAW;IAEf,MAAM,WAAW,OAAO;QACtB;QAEA,MAAM,cAA4B;YAChC,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;QACzB;QAEA,MAAM,UAAU,MAAM,MAAM;QAE5B,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,2BAA2B;QAC/B,gBAAgB,CAAC;IACnB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAuB;;;;;;8CAC5C,6LAAC,mIAAA,CAAA,kBAAe;oCAAC,WAAU;8CAAc;;;;;;;;;;;;sCAI3C,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAE,GAAG,IAAI;0CACZ,cAAA,6LAAC;oCAAK,UAAU,KAAK,YAAY,CAAC;oCAAW,WAAU;;sDAErD,6LAAC,mIAAA,CAAA,YAAS;4CACR,SAAS,KAAK,OAAO;4CACrB,MAAK;4CACL,QAAQ;oDAAC,EAAE,KAAK,EAAE;qEAChB,6LAAC,mIAAA,CAAA,WAAQ;;sEACP,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAG9B,6LAAC,mIAAA,CAAA,cAAW;sEACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gEACH,GAAG,KAAK;gEACT,MAAK;gEACL,aAAY;gEACZ,UAAU;gEACV,WAAU;gEACV,KAAI;;;;;;;;;;;sEAGR,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;sDAMlB,6LAAC,mIAAA,CAAA,YAAS;4CACR,SAAS,KAAK,OAAO;4CACrB,MAAK;4CACL,QAAQ;oDAAC,EAAE,KAAK,EAAE;qEAChB,6LAAC,mIAAA,CAAA,WAAQ;;sEACP,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAG9B,6LAAC,mIAAA,CAAA,cAAW;sEACV,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;wEACH,GAAG,KAAK;wEACT,MAAM,eAAe,SAAS;wEAC9B,aAAY;wEACZ,UAAU;wEACV,WAAU;;;;;;kFAEZ,6LAAC;wEACC,MAAK;wEACL,SAAS;wEACT,WAAU;wEACV,UAAU;kFAET,6BACC,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;mGAElB,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sEAKvB,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;wCAMjB,uBACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;;;;;;sDAKrD,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU;sDAET,0BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;oDAAiF;;;;;;qEAIlG,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAOnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqD;;;;;;8DAGlE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;;;;;;;;;;;;;sDAKP,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDAAI,WAAU;;wDAAwB;wDACtB;sEACf,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAO/C;GAzMwB;;QACP,qIAAA,CAAA,YAAS;QACyC,wHAAA,CAAA,UAAO;QAG3D,iKAAA,CAAA,UAAO;;;KALE", "debugId": null}}]}