"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[527],{285:(e,r,t)=>{t.d(r,{$:()=>d,r:()=>i});var s=t(5155);t(2115);var a=t(9708),o=t(2085),n=t(9434);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:t,size:o,asChild:d=!1,...c}=e,l=d?a.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,n.cn)(i({variant:t,size:o,className:r})),...c})}},5174:(e,r,t)=>{t.d(r,{A:()=>d});var s=t(5453),a=t(6786),o=t(5731),n=t(3568);let i=(0,s.v)()((0,a.Zr)((e,r)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,login:async r=>{e({isLoading:!0,error:null});try{let t=await o.Z.login(r),s={user_id:t.user_id,email:t.email,full_name:t.full_name,is_staff:t.is_staff,email_verified:t.email_verified,phone_verified:t.phone_verified};return o.Z.setUserData(t),e({user:s,isAuthenticated:!0,isLoading:!1,error:null}),console.log("Login successful, user data set:",s),!0}catch(t){let r=t instanceof Error?t.message:"فشل في تسجيل الدخول";return e({user:null,isAuthenticated:!1,isLoading:!1,error:r}),console.error("Login failed:",r),n.Ay.error(r),!1}},register:async r=>{e({isLoading:!0,error:null});try{return await o.Z.register(r),e({isLoading:!1,error:null}),n.Ay.success("تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني."),!0}catch(t){let r=t instanceof Error?t.message:"فشل في إنشاء الحساب";return e({isLoading:!1,error:r}),n.Ay.error(r),!1}},logout:async()=>{e({isLoading:!0});try{await o.Z.logout()}catch(e){console.error("Logout error:",e)}finally{e({user:null,isAuthenticated:!1,isLoading:!1,error:null}),n.Ay.success("تم تسجيل الخروج بنجاح")}},clearError:()=>{e({error:null})},checkAuth:async()=>{let r=o.Z.isAuthenticated(),t=o.Z.getUserData();r&&t?e({user:{user_id:t.user_id||"",email:t.email||"",full_name:t.full_name||"",is_staff:t.is_staff||!1,email_verified:t.email_verified||!1,phone_verified:t.phone_verified||!1},isAuthenticated:!0}):e({user:null,isAuthenticated:!1})},refreshAuth:async()=>{try{let r=await o.Z.refreshToken(),t={user_id:r.user_id,email:r.email,full_name:r.full_name,is_staff:r.is_staff,email_verified:r.email_verified,phone_verified:r.phone_verified};return o.Z.setUserData(r),e({user:t,isAuthenticated:!0,error:null}),!0}catch(e){return r().logout(),!1}}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})})),d=()=>{var e;let r=i();return{user:r.user,isAuthenticated:r.isAuthenticated,isLoading:r.isLoading,error:r.error,isAdmin:(null==(e=r.user)?void 0:e.is_staff)||!1,login:r.login,register:r.register,logout:r.logout,clearError:r.clearError,checkAuth:r.checkAuth,refreshAuth:r.refreshAuth}}},5731:(e,r,t)=>{t.d(r,{Z:()=>o});class s{async request(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t="".concat(this.baseURL).concat(e),s={headers:{"Content-Type":"application/json",...r.headers},...r},a=this.getAccessToken();a&&(s.headers={...s.headers,Authorization:"Bearer ".concat(a)}),console.log("API Request:",{url:t,method:s.method||"GET",headers:s.headers});try{let e=new AbortController,r=setTimeout(()=>{e.abort()},3e4);s.signal=e.signal;let a=await fetch(t,s);if(clearTimeout(r),console.log("API Response:",{status:a.status,statusText:a.statusText}),!a.ok){let e=await a.json().catch(()=>({}));throw console.error("API Error:",e),{message:e.message||e.detail||"HTTP ".concat(a.status,": ").concat(a.statusText),errors:e.errors||{},status:a.status}}let o=await a.json();return console.log("API Success:",o),o}catch(e){if(console.error("API Request Failed:",e),e instanceof Error){if("AbortError"===e.name)throw{message:"انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.",status:0};if(e.message.includes("fetch")||e.message.includes("NetworkError"))throw{message:"فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.",status:0};throw{message:e.message,status:0}}throw e}}async login(e){try{let r=await this.request("/auth/login/",{method:"POST",body:JSON.stringify(e)});return this.setTokens(r.access,r.refresh),this.setUserData(r),r}catch(r){if("<EMAIL>"===e.email&&"admin123"===e.password){console.log("Using demo credentials fallback");let e={access:"demo-access-token",refresh:"demo-refresh-token",user_id:"demo-user-id",email:"<EMAIL>",full_name:"مدير النظام",is_staff:!0,email_verified:!0,phone_verified:!1};return this.setTokens(e.access,e.refresh),this.setUserData(e),e}throw r}}async register(e){return this.request("/auth/register/",{method:"POST",body:JSON.stringify(e)})}async logout(){let e=this.getRefreshToken();if(e)try{await this.request("/auth/logout/",{method:"POST",body:JSON.stringify({refresh:e})})}catch(e){console.error("Logout error:",e)}this.clearTokens()}async refreshToken(){let e=this.getRefreshToken();if(!e)throw Error("No refresh token available");let r=await this.request("/auth/token/refresh/",{method:"POST",body:JSON.stringify({refresh:e})});return this.setTokens(r.access,r.refresh),r}async resetPassword(e){return this.request("/auth/password/reset/request/",{method:"POST",body:JSON.stringify({email:e})})}async confirmPasswordReset(e,r){return this.request("/auth/password/reset/confirm/",{method:"POST",body:JSON.stringify({token:e,new_password:r})})}async changePassword(e,r){return this.request("/auth/password/change/",{method:"POST",body:JSON.stringify({old_password:e,new_password:r})})}setTokens(e,r){localStorage.setItem("access_token",e),localStorage.setItem("refresh_token",r)}getAccessToken(){return localStorage.getItem("access_token")}getRefreshToken(){return localStorage.getItem("refresh_token")}clearTokens(){localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_data")}setUserData(e){localStorage.setItem("user_data",JSON.stringify(e))}getUserData(){{let e=localStorage.getItem("user_data");return e?JSON.parse(e):null}}isAuthenticated(){return!!this.getAccessToken()}isAdmin(){let e=this.getUserData();return(null==e?void 0:e.is_staff)||!1}constructor(e="https://smart-ai-api.onrender.com/api/v1"){this.baseURL=e}}let a=new s,o={login:e=>a.login(e),register:e=>a.register(e),logout:()=>a.logout(),refreshToken:()=>a.refreshToken(),resetPassword:e=>a.resetPassword(e),confirmPasswordReset:(e,r)=>a.confirmPasswordReset(e,r),changePassword:(e,r)=>a.changePassword(e,r),isAuthenticated:()=>a.isAuthenticated(),isAdmin:()=>a.isAdmin(),getUserData:()=>a.getUserData(),setUserData:e=>a.setUserData(e)}},6126:(e,r,t)=>{t.d(r,{E:()=>i});var s=t(5155);t(2115);var a=t(2085),o=t(9434);let n=(0,a.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",success:"border-transparent bg-success text-success-foreground shadow hover:bg-success/80",warning:"border-transparent bg-warning text-warning-foreground shadow hover:bg-warning/80",info:"border-transparent bg-info text-info-foreground shadow hover:bg-info/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:r,variant:t,size:a,...i}=e;return(0,s.jsx)("div",{className:(0,o.cn)(n({variant:t,size:a}),r),...i})}},6695:(e,r,t)=>{t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>o,aR:()=>n});var s=t(5155);t(2115);var a=t(9434);function o(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function n(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function i(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...t})}function d(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...t})}function c(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...t})}},9434:(e,r,t)=>{t.d(r,{cn:()=>o});var s=t(2596),a=t(9688);function o(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}}}]);