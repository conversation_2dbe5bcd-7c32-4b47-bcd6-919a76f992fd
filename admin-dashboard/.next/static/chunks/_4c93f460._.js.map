{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/auth/protected-route.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/stores/auth';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requireAdmin?: boolean;\n}\n\nexport function ProtectedRoute({ children, requireAdmin = false }: ProtectedRouteProps) {\n  const router = useRouter();\n  const { isAuthenticated, isAdmin, isLoading, checkAuth } = useAuth();\n\n  useEffect(() => {\n    checkAuth();\n  }, [checkAuth]);\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (!isAuthenticated) {\n        router.push('/login');\n        return;\n      }\n\n      if (requireAdmin && !isAdmin) {\n        router.push('/admin/dashboard');\n        return;\n      }\n    }\n  }, [isAuthenticated, isAdmin, isLoading, requireAdmin, router]);\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  // Don't render children if not authenticated or not admin (when required)\n  if (!isAuthenticated || (requireAdmin && !isAdmin)) {\n    return null;\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,eAAe,KAAuD;QAAvD,EAAE,QAAQ,EAAE,eAAe,KAAK,EAAuB,GAAvD;;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC;KAAU;IAEd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,WAAW;gBACd,IAAI,CAAC,iBAAiB;oBACpB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,gBAAgB,CAAC,SAAS;oBAC5B,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;mCAAG;QAAC;QAAiB;QAAS;QAAW;QAAc;KAAO;IAE9D,qDAAqD;IACrD,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,0EAA0E;IAC1E,IAAI,CAAC,mBAAoB,gBAAgB,CAAC,SAAU;QAClD,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GArCgB;;QACC,qIAAA,CAAA,YAAS;QACmC,wHAAA,CAAA,UAAO;;;KAFpD", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}