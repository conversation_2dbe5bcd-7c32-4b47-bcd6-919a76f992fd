"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[138],{381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},646:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},704:(e,t,n)=>{n.d(t,{B8:()=>T,UC:()=>N,bL:()=>A,l9:()=>M});var r=n(2115),o=n(5185),i=n(6081),l=n(9196),a=n(8905),u=n(3655),s=n(4315),c=n(5845),d=n(1285),f=n(5155),p="Tabs",[h,v]=(0,i.A)(p,[l.RG]),m=(0,l.RG)(),[g,y]=h(p),w=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:l="horizontal",dir:a,activationMode:h="automatic",...v}=e,m=(0,s.jH)(a),[y,w]=(0,c.i)({prop:r,onChange:o,defaultProp:null!=i?i:"",caller:p});return(0,f.jsx)(g,{scope:n,baseId:(0,d.B)(),value:y,onValueChange:w,orientation:l,dir:m,activationMode:h,children:(0,f.jsx)(u.sG.div,{dir:m,"data-orientation":l,...v,ref:t})})});w.displayName=p;var x="TabsList",b=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,i=y(x,n),a=m(n);return(0,f.jsx)(l.bL,{asChild:!0,...a,orientation:i.orientation,dir:i.dir,loop:r,children:(0,f.jsx)(u.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});b.displayName=x;var C="TabsTrigger",E=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...a}=e,s=y(C,n),c=m(n),d=S(s.baseId,r),p=j(s.baseId,r),h=r===s.value;return(0,f.jsx)(l.q7,{asChild:!0,...c,focusable:!i,active:h,children:(0,f.jsx)(u.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":p,"data-state":h?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:d,...a,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==s.activationMode;h||i||!e||s.onValueChange(r)})})})});E.displayName=C;var R="TabsContent",k=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:i,children:l,...s}=e,c=y(R,n),d=S(c.baseId,o),p=j(c.baseId,o),h=o===c.value,v=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(a.C,{present:i||h,children:n=>{let{present:r}=n;return(0,f.jsx)(u.sG.div,{"data-state":h?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:p,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:r&&l})}})});function S(e,t){return"".concat(e,"-trigger-").concat(t)}function j(e,t){return"".concat(e,"-content-").concat(t)}k.displayName=R;var A=w,T=b,M=E,N=k},1243:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1275:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(2115),o=n(2712);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},1284:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},1285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(2115),i=n(2712),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},2293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(2115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:l()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2355:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},2564:(e,t,n)=>{n.d(t,{Qg:()=>l,bL:()=>u});var r=n(2115),o=n(3655),i=n(5155),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var u=a},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},3052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3795:(e,t,n)=>{n.d(t,{A:()=>U});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(2115)),u="right-scroll-bar-position",s="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=p),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return o.options=i({async:!0,ssr:!1},e),o}(),v=function(){},m=a.forwardRef(function(e,t){var n,r,o,u,s=a.useRef(null),p=a.useState({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:v}),m=p[0],g=p[1],y=e.forwardProps,w=e.children,x=e.className,b=e.removeScrollBar,C=e.enabled,E=e.shards,R=e.sideCar,k=e.noRelative,S=e.noIsolation,j=e.inert,A=e.allowPinchZoom,T=e.as,M=e.gapMode,N=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=(n=[s,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}f.set(u,n)},[n]),u),L=i(i({},N),m);return a.createElement(a.Fragment,null,C&&a.createElement(R,{sideCar:h,removeScrollBar:b,shards:E,noRelative:k,noIsolation:S,inert:j,setCallbacks:g,allowPinchZoom:!!A,lockRef:s,gapMode:M}),y?a.cloneElement(a.Children.only(w),i(i({},L),{ref:D})):a.createElement(void 0===T?"div":T,i({},L,{className:x,ref:D}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:s,zeroRight:u};var g=function(e){var t=e.sideCar,n=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},x=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},C=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[C(n),C(r),C(o)]},R=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},k=x(),S="data-scroll-locked",j=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},T=function(){a.useEffect(function(){return document.body.setAttribute(S,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},M=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var i=a.useMemo(function(){return R(o)},[o]);return a.createElement(k,{styles:j(i,!t,o,n?"":"!important")})},N=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return N=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){N=!1}var L=!!N&&{passive:!1},P=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},O=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=_(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?P(t,"overflowY"):P(t,"overflowX")},_=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,s=t.contains(u),c=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=_(e,u),v=h[0],m=h[1]-h[2]-l*v;(v||m)&&I(e,u)&&(f+=m,p+=v);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},G=function(e){return[e.deltaX,e.deltaY]},H=function(e){return e&&"current"in e?e.current:e},W=0,z=[];let K=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(W++)[0],i=a.useState(x)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(H),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=B(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],s="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=O(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=O(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return F(p,t,e,"h"===p?u:s,!0)},[]),s=a.useCallback(function(e){if(z.length&&z[z.length-1]===i){var n="deltaY"in e?G(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(H).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=B(e),r.current=void 0},[]),f=a.useCallback(function(t){c(t.type,G(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,B(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return z.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,L),document.addEventListener("touchmove",s,L),document.addEventListener("touchstart",d,L),function(){z=z.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,L),document.removeEventListener("touchmove",s,L),document.removeEventListener("touchstart",d,L)}},[]);var h=e.removeScrollBar,v=e.inert;return a.createElement(a.Fragment,null,v?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(M,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),g);var V=a.forwardRef(function(e,t){return a.createElement(m,i({},e,{ref:t,sideCar:K}))});V.classNames=m.classNames;let U=V},3904:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4059:(e,t,n)=>{n.d(t,{C1:()=>B,bL:()=>_,q7:()=>F});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(3655),u=n(9196),s=n(5845),c=n(4315),d=n(1275),f=n(5503),p=n(8905),h=n(5155),v="Radio",[m,g]=(0,l.A)(v),[y,w]=m(v),x=r.forwardRef((e,t)=>{let{__scopeRadio:n,name:l,checked:u=!1,required:s,disabled:c,value:d="on",onCheck:f,form:p,...v}=e,[m,g]=r.useState(null),w=(0,i.s)(t,e=>g(e)),x=r.useRef(!1),b=!m||p||!!m.closest("form");return(0,h.jsxs)(y,{scope:n,checked:u,disabled:c,children:[(0,h.jsx)(a.sG.button,{type:"button",role:"radio","aria-checked":u,"data-state":R(u),"data-disabled":c?"":void 0,disabled:c,value:d,...v,ref:w,onClick:(0,o.m)(e.onClick,e=>{u||null==f||f(),b&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})}),b&&(0,h.jsx)(E,{control:m,bubbles:!x.current,name:l,value:d,checked:u,required:s,disabled:c,form:p,style:{transform:"translateX(-100%)"}})]})});x.displayName=v;var b="RadioIndicator",C=r.forwardRef((e,t)=>{let{__scopeRadio:n,forceMount:r,...o}=e,i=w(b,n);return(0,h.jsx)(p.C,{present:r||i.checked,children:(0,h.jsx)(a.sG.span,{"data-state":R(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t})})});C.displayName=b;var E=r.forwardRef((e,t)=>{let{__scopeRadio:n,control:o,checked:l,bubbles:u=!0,...s}=e,c=r.useRef(null),p=(0,i.s)(c,t),v=(0,f.Z)(l),m=(0,d.X)(o);return r.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==l&&t){let n=new Event("click",{bubbles:u});t.call(e,l),e.dispatchEvent(n)}},[v,l,u]),(0,h.jsx)(a.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:l,...s,tabIndex:-1,ref:p,style:{...s.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function R(e){return e?"checked":"unchecked"}E.displayName="RadioBubbleInput";var k=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],S="RadioGroup",[j,A]=(0,l.A)(S,[u.RG,g]),T=(0,u.RG)(),M=g(),[N,D]=j(S),L=r.forwardRef((e,t)=>{let{__scopeRadioGroup:n,name:r,defaultValue:o,value:i,required:l=!1,disabled:d=!1,orientation:f,dir:p,loop:v=!0,onValueChange:m,...g}=e,y=T(n),w=(0,c.jH)(p),[x,b]=(0,s.i)({prop:i,defaultProp:null!=o?o:null,onChange:m,caller:S});return(0,h.jsx)(N,{scope:n,name:r,required:l,disabled:d,value:x,onValueChange:b,children:(0,h.jsx)(u.bL,{asChild:!0,...y,orientation:f,dir:w,loop:v,children:(0,h.jsx)(a.sG.div,{role:"radiogroup","aria-required":l,"aria-orientation":f,"data-disabled":d?"":void 0,dir:w,...g,ref:t})})})});L.displayName=S;var P="RadioGroupItem",O=r.forwardRef((e,t)=>{let{__scopeRadioGroup:n,disabled:l,...a}=e,s=D(P,n),c=s.disabled||l,d=T(n),f=M(n),p=r.useRef(null),v=(0,i.s)(t,p),m=s.value===a.value,g=r.useRef(!1);return r.useEffect(()=>{let e=e=>{k.includes(e.key)&&(g.current=!0)},t=()=>g.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,h.jsx)(u.q7,{asChild:!0,...d,focusable:!c,active:m,children:(0,h.jsx)(x,{disabled:c,required:s.required,checked:m,...f,...a,name:s.name,ref:v,onCheck:()=>s.onValueChange(a.value),onKeyDown:(0,o.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.m)(a.onFocus,()=>{var e;g.current&&(null==(e=p.current)||e.click())})})})});O.displayName=P;var I=r.forwardRef((e,t)=>{let{__scopeRadioGroup:n,...r}=e,o=M(n);return(0,h.jsx)(C,{...o,...r,ref:t})});I.displayName="RadioGroupIndicator";var _=L,F=O,B=I},4315:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(2115);n(5155);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},4378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(2115),o=n(7650),i=n(3655),l=n(2712),a=n(5155),u=r.forwardRef((e,t)=>{var n,u;let{container:s,...c}=e,[d,f]=r.useState(!1);(0,l.N)(()=>f(!0),[]);let p=s||d&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...c,ref:t}),p):null});u.displayName="Portal"},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4582:(e,t,n)=>{n.d(t,{UC:()=>eO,YJ:()=>e_,In:()=>eL,q7:()=>eB,VF:()=>eH,p4:()=>eG,JU:()=>eF,ZL:()=>eP,bL:()=>eM,wn:()=>ez,PP:()=>eW,wv:()=>eK,l9:()=>eN,WT:()=>eD,LM:()=>eI});var r=n(2115),o=n(7650);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var l=n(5185),a=n(7328),u=n(6101),s=n(6081),c=n(4315),d=n(9178),f=n(2293),p=n(7900),h=n(1285),v=n(5152),m=n(4378),g=n(3655),y=n(9708),w=n(9033),x=n(5845),b=n(2712),C=n(5503),E=n(2564),R=n(8168),k=n(3795),S=n(5155),j=[" ","Enter","ArrowUp","ArrowDown"],A=[" ","Enter"],T="Select",[M,N,D]=(0,a.N)(T),[L,P]=(0,s.A)(T,[D,v.Bk]),O=(0,v.Bk)(),[I,_]=L(T),[F,B]=L(T),G=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:u,onValueChange:s,dir:d,name:f,autoComplete:p,disabled:m,required:g,form:y}=e,w=O(t),[b,C]=r.useState(null),[E,R]=r.useState(null),[k,j]=r.useState(!1),A=(0,c.jH)(d),[N,D]=(0,x.i)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:T}),[L,P]=(0,x.i)({prop:a,defaultProp:u,onChange:s,caller:T}),_=r.useRef(null),B=!b||y||!!b.closest("form"),[G,H]=r.useState(new Set),W=Array.from(G).map(e=>e.props.value).join(";");return(0,S.jsx)(v.bL,{...w,children:(0,S.jsxs)(I,{required:g,scope:t,trigger:b,onTriggerChange:C,valueNode:E,onValueNodeChange:R,valueNodeHasChildren:k,onValueNodeHasChildrenChange:j,contentId:(0,h.B)(),value:L,onValueChange:P,open:N,onOpenChange:D,dir:A,triggerPointerDownPosRef:_,disabled:m,children:[(0,S.jsx)(M.Provider,{scope:t,children:(0,S.jsx)(F,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{H(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{H(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),B?(0,S.jsxs)(eS,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:L,onChange:e=>P(e.target.value),disabled:m,form:y,children:[void 0===L?(0,S.jsx)("option",{value:""}):null,Array.from(G)]},W):null]})})};G.displayName=T;var H="SelectTrigger",W=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=O(n),s=_(H,n),c=s.disabled||o,d=(0,u.s)(t,s.onTriggerChange),f=N(n),p=r.useRef("touch"),[h,m,y]=eA(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=eT(t,e,n);void 0!==r&&s.onValueChange(r.value)}),w=e=>{c||(s.onOpenChange(!0),y()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,S.jsx)(v.Mz,{asChild:!0,...a,children:(0,S.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":ej(s.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&w(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&j.includes(e.key)&&(w(),e.preventDefault())})})})});W.displayName=H;var z="SelectValue",K=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,s=_(z,n),{onValueNodeHasChildrenChange:c}=s,d=void 0!==i,f=(0,u.s)(t,s.onValueNodeChange);return(0,b.N)(()=>{c(d)},[c,d]),(0,S.jsx)(g.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:ej(s.value)?(0,S.jsx)(S.Fragment,{children:l}):i})});K.displayName=z;var V=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,S.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});V.displayName="SelectIcon";var U=e=>(0,S.jsx)(m.Z,{asChild:!0,...e});U.displayName="SelectPortal";var q="SelectContent",X=r.forwardRef((e,t)=>{let n=_(q,e.__scopeSelect),[i,l]=r.useState();return((0,b.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,S.jsx)(J,{...e,ref:t}):i?o.createPortal((0,S.jsx)(Y,{scope:e.__scopeSelect,children:(0,S.jsx)(M.Slot,{scope:e.__scopeSelect,children:(0,S.jsx)("div",{children:e.children})})}),i):null});X.displayName=q;var[Y,Z]=L(q),$=(0,y.TL)("SelectContent.RemoveScroll"),J=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:s,side:c,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:C,...E}=e,j=_(q,n),[A,T]=r.useState(null),[M,D]=r.useState(null),L=(0,u.s)(t,e=>T(e)),[P,O]=r.useState(null),[I,F]=r.useState(null),B=N(n),[G,H]=r.useState(!1),W=r.useRef(!1);r.useEffect(()=>{if(A)return(0,R.Eq)(A)},[A]),(0,f.Oh)();let z=r.useCallback(e=>{let[t,...n]=B().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&M&&(M.scrollTop=0),n===r&&M&&(M.scrollTop=M.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[B,M]),K=r.useCallback(()=>z([P,A]),[z,P,A]);r.useEffect(()=>{G&&K()},[G,K]);let{onOpenChange:V,triggerPointerDownPosRef:U}=j;r.useEffect(()=>{if(A){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=U.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(i=null==(r=U.current)?void 0:r.y)?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():A.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[A,V,U]),r.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[X,Z]=eA(e=>{let t=B().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eT(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),J=r.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==j.value&&j.value===t||r)&&(O(e),r&&(W.current=!0))},[j.value]),et=r.useCallback(()=>null==A?void 0:A.focus(),[A]),en=r.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==j.value&&j.value===t||r)&&F(e)},[j.value]),er="popper"===o?ee:Q,eo=er===ee?{side:c,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:C}:{};return(0,S.jsx)(Y,{scope:n,content:A,viewport:M,onViewportChange:D,itemRefCallback:J,selectedItem:P,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:K,selectedItemText:I,position:o,isPositioned:G,searchRef:X,children:(0,S.jsx)(k.A,{as:$,allowPinchZoom:!0,children:(0,S.jsx)(p.n,{asChild:!0,trapped:j.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{var t;null==(t=j.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,S.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:(0,S.jsx)(er,{role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:e=>e.preventDefault(),...E,...eo,onPlaced:()=>H(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:(0,l.m)(E.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=B().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=_(q,n),s=Z(q,n),[c,d]=r.useState(null),[f,p]=r.useState(null),h=(0,u.s)(t,e=>p(e)),v=N(n),m=r.useRef(!1),y=r.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:C,focusSelectedItem:E}=s,R=r.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&f&&w&&x&&C){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=C.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,u=e.width+a,s=Math.max(u,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=u+"px",c.style.left=d+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,u=e.width+a,s=Math.max(u,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=u+"px",c.style.right=d+"px"}let l=v(),u=window.innerHeight-20,s=w.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),y=p+h+s+parseInt(d.paddingBottom,10)+g,b=Math.min(5*x.offsetHeight,y),E=window.getComputedStyle(w),R=parseInt(E.paddingTop,10),k=parseInt(E.paddingBottom,10),S=e.top+e.height/2-10,j=x.offsetHeight/2,A=p+h+(x.offsetTop+j);if(A<=S){let e=l.length>0&&x===l[l.length-1].ref.current;c.style.bottom="0px";let t=Math.max(u-S,j+(e?k:0)+(f.clientHeight-w.offsetTop-w.offsetHeight)+g);c.style.height=A+t+"px"}else{let e=l.length>0&&x===l[0].ref.current;c.style.top="0px";let t=Math.max(S,p+w.offsetTop+(e?R:0)+j);c.style.height=t+(y-A)+"px",w.scrollTop=A-S+w.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=b+"px",c.style.maxHeight=u+"px",null==o||o(),requestAnimationFrame(()=>m.current=!0)}},[v,a.trigger,a.valueNode,c,f,w,x,C,a.dir,o]);(0,b.N)(()=>R(),[R]);let[k,j]=r.useState();(0,b.N)(()=>{f&&j(window.getComputedStyle(f).zIndex)},[f]);let A=r.useCallback(e=>{e&&!0===y.current&&(R(),null==E||E(),y.current=!1)},[R,E]);return(0,S.jsx)(et,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:m,onScrollButtonChange:A,children:(0,S.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:(0,S.jsx)(g.sG.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=O(n);return(0,S.jsx)(v.UC,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,en]=L(q,{}),er="SelectViewport",eo=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=Z(er,n),s=en(er,n),c=(0,u.s)(t,a.onViewportChange),d=r.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,S.jsx)(M.Slot,{scope:n,children:(0,S.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if((null==r?void 0:r.current)&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=er;var ei="SelectGroup",[el,ea]=L(ei),eu=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,S.jsx)(el,{scope:n,id:o,children:(0,S.jsx)(g.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});eu.displayName=ei;var es="SelectLabel",ec=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ea(es,n);return(0,S.jsx)(g.sG.div,{id:o.id,...r,ref:t})});ec.displayName=es;var ed="SelectItem",[ef,ep]=L(ed),eh=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...s}=e,c=_(ed,n),d=Z(ed,n),f=c.value===o,[p,v]=r.useState(null!=a?a:""),[m,y]=r.useState(!1),w=(0,u.s)(t,e=>{var t;return null==(t=d.itemRefCallback)?void 0:t.call(d,e,o,i)}),x=(0,h.B)(),b=r.useRef("touch"),C=()=>{i||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(ef,{scope:n,value:o,disabled:i,textId:x,isSelected:f,onItemTextChange:r.useCallback(e=>{v(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,S.jsx)(M.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,S.jsx)(g.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":f&&m,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...s,ref:w,onFocus:(0,l.m)(s.onFocus,()=>y(!0)),onBlur:(0,l.m)(s.onBlur,()=>y(!1)),onClick:(0,l.m)(s.onClick,()=>{"mouse"!==b.current&&C()}),onPointerUp:(0,l.m)(s.onPointerUp,()=>{"mouse"===b.current&&C()}),onPointerDown:(0,l.m)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.m)(s.onPointerMove,e=>{if(b.current=e.pointerType,i){var t;null==(t=d.onItemLeave)||t.call(d)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(s.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=d.onItemLeave)||t.call(d)}}),onKeyDown:(0,l.m)(s.onKeyDown,e=>{var t;((null==(t=d.searchRef)?void 0:t.current)===""||" "!==e.key)&&(A.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ed;var ev="SelectItemText",em=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,s=_(ev,n),c=Z(ev,n),d=ep(ev,n),f=B(ev,n),[p,h]=r.useState(null),v=(0,u.s)(t,e=>h(e),d.onItemTextChange,e=>{var t;return null==(t=c.itemTextRefCallback)?void 0:t.call(c,e,d.value,d.disabled)}),m=null==p?void 0:p.textContent,y=r.useMemo(()=>(0,S.jsx)("option",{value:d.value,disabled:d.disabled,children:m},d.value),[d.disabled,d.value,m]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=f;return(0,b.N)(()=>(w(y),()=>x(y)),[w,x,y]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(g.sG.span,{id:d.textId,...a,ref:v}),d.isSelected&&s.valueNode&&!s.valueNodeHasChildren?o.createPortal(a.children,s.valueNode):null]})});em.displayName=ev;var eg="SelectItemIndicator",ey=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ep(eg,n).isSelected?(0,S.jsx)(g.sG.span,{"aria-hidden":!0,...r,ref:t}):null});ey.displayName=eg;var ew="SelectScrollUpButton",ex=r.forwardRef((e,t)=>{let n=Z(ew,e.__scopeSelect),o=en(ew,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,S.jsx)(eE,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ex.displayName=ew;var eb="SelectScrollDownButton",eC=r.forwardRef((e,t)=>{let n=Z(eb,e.__scopeSelect),o=en(eb,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,S.jsx)(eE,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eC.displayName=eb;var eE=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=Z("SelectScrollButton",n),u=r.useRef(null),s=N(n),c=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,b.N)(()=>{var e;let t=s().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[s]),(0,S.jsx)(g.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{c()})})}),eR=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,S.jsx)(g.sG.div,{"aria-hidden":!0,...r,ref:t})});eR.displayName="SelectSeparator";var ek="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=O(n),i=_(ek,n),l=Z(ek,n);return i.open&&"popper"===l.position?(0,S.jsx)(v.i3,{...o,...r,ref:t}):null}).displayName=ek;var eS=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,...i}=e,l=r.useRef(null),a=(0,u.s)(t,l),s=(0,C.Z)(o);return r.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==o&&t){let n=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(n)}},[s,o]),(0,S.jsx)(g.sG.select,{...i,style:{...E.Qg,...i.style},ref:a,defaultValue:o})});function ej(e){return""===e||void 0===e}function eA(e){let t=(0,w.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function eT(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}eS.displayName="SelectBubbleInput";var eM=G,eN=W,eD=K,eL=V,eP=U,eO=X,eI=eo,e_=eu,eF=ec,eB=eh,eG=em,eH=ey,eW=ex,ez=eC,eK=eR},4884:(e,t,n)=>{n.d(t,{bL:()=>C,zi:()=>E});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(5845),u=n(5503),s=n(1275),c=n(3655),d=n(5155),f="Switch",[p,h]=(0,l.A)(f),[v,m]=p(f),g=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:l,checked:u,defaultChecked:s,required:p,disabled:h,value:m="on",onCheckedChange:g,form:y,...w}=e,[C,E]=r.useState(null),R=(0,i.s)(t,e=>E(e)),k=r.useRef(!1),S=!C||y||!!C.closest("form"),[j,A]=(0,a.i)({prop:u,defaultProp:null!=s&&s,onChange:g,caller:f});return(0,d.jsxs)(v,{scope:n,checked:j,disabled:h,children:[(0,d.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":j,"aria-required":p,"data-state":b(j),"data-disabled":h?"":void 0,disabled:h,value:m,...w,ref:R,onClick:(0,o.m)(e.onClick,e=>{A(e=>!e),S&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),S&&(0,d.jsx)(x,{control:C,bubbles:!k.current,name:l,value:m,checked:j,required:p,disabled:h,form:y,style:{transform:"translateX(-100%)"}})]})});g.displayName=f;var y="SwitchThumb",w=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,o=m(y,n);return(0,d.jsx)(c.sG.span,{"data-state":b(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});w.displayName=y;var x=r.forwardRef((e,t)=>{let{__scopeSwitch:n,control:o,checked:l,bubbles:a=!0,...c}=e,f=r.useRef(null),p=(0,i.s)(f,t),h=(0,u.Z)(l),v=(0,s.X)(o);return r.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==l&&t){let n=new Event("click",{bubbles:a});t.call(e,l),e.dispatchEvent(n)}},[h,l,a]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...c,tabIndex:-1,ref:p,style:{...c.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}x.displayName="SwitchBubbleInput";var C=g,E=w},5152:(e,t,n)=>{n.d(t,{Mz:()=>e1,i3:()=>e5,UC:()=>e2,bL:()=>e0,Bk:()=>eF});var r=n(2115);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,u=Math.floor,s=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function v(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}let x=["left","right"],b=["right","left"],C=["top","bottom"],E=["bottom","top"];function R(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function k(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function S(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function j(e,t,n){let r,{reference:o,floating:i}=e,l=y(t),a=v(y(t)),u=m(a),s=p(t),c="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,g=o[u]/2-i[u]/2;switch(s){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=g*(n&&c?-1:1);break;case"end":r[a]+=g*(n&&c?-1:1)}return r}let A=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=j(s,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:v}=a[n],{x:m,y:g,data:y,reset:w}=await v({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=m?m:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=j(s,f,u)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function T(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),v=k(h),m=a[p?"floating"===d?"reference":"floating":d],g=S(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:u})),y="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),x=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},b=S(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-b.top+v.top)/x.y,bottom:(b.bottom-g.bottom+v.bottom)/x.y,left:(g.left-b.left+v.left)/x.x,right:(b.right-g.right+v.right)/x.x}}function M(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function N(e){return o.some(t=>e[t]>=0)}let D=new Set(["left","top"]);async function L(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=h(n),u="y"===y(n),s=D.has(l)?-1:1,c=i&&u?-1:1,d=f(t,e),{mainAxis:v,crossAxis:m,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof g&&(m="end"===a?-1*g:g),u?{x:m*c,y:v*s}:{x:v*s,y:m*c}}function P(){return"undefined"!=typeof window}function O(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function I(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function _(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!P()&&(e instanceof Node||e instanceof I(e).Node)}function B(e){return!!P()&&(e instanceof Element||e instanceof I(e).Element)}function G(e){return!!P()&&(e instanceof HTMLElement||e instanceof I(e).HTMLElement)}function H(e){return!!P()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof I(e).ShadowRoot)}let W=new Set(["inline","contents"]);function z(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!W.has(o)}let K=new Set(["table","td","th"]),V=[":popover-open",":modal"];function U(e){return V.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let q=["transform","translate","scale","rotate","perspective"],X=["transform","translate","scale","rotate","perspective","filter"],Y=["paint","layout","strict","content"];function Z(e){let t=$(),n=B(e)?ee(e):e;return q.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||X.some(e=>(n.willChange||"").includes(e))||Y.some(e=>(n.contain||"").includes(e))}function $(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let J=new Set(["html","body","#document"]);function Q(e){return J.has(O(e))}function ee(e){return I(e).getComputedStyle(e)}function et(e){return B(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===O(e))return e;let t=e.assignedSlot||e.parentNode||H(e)&&e.host||_(e);return H(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return Q(n)?t.ownerDocument?t.ownerDocument.body:t.body:G(n)&&z(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=I(o);if(i){let e=eo(l);return t.concat(l,l.visualViewport||[],z(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=G(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,u=a(n)!==i||a(r)!==l;return u&&(n=i,r=l),{width:n,height:r,$:u}}function el(e){return B(e)?e:e.contextElement}function ea(e){let t=el(e);if(!G(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ei(t),l=(i?a(n.width):n.width)/r,u=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let eu=s(0);function es(e){let t=I(e);return $()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function ec(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=el(e),a=s(1);t&&(r?B(r)&&(a=ea(r)):a=ea(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===I(l))&&o)?es(l):s(0),c=(i.left+u.x)/a.x,d=(i.top+u.y)/a.y,f=i.width/a.x,p=i.height/a.y;if(l){let e=I(l),t=r&&B(r)?I(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=ea(o),t=o.getBoundingClientRect(),r=ee(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=i,d+=l,o=eo(n=I(o))}}return S({width:f,height:p,x:c,y:d})}function ed(e,t){let n=et(e).scrollLeft;return t?t.left+n:ec(_(e)).left+n}function ef(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ed(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function eh(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=I(e),r=_(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=$();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=_(e),n=et(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ed(e),u=-n.scrollTop;return"rtl"===ee(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:u}}(_(e));else if(B(t))r=function(e,t){let n=ec(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=G(e)?ea(e):s(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=es(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return S(r)}function ev(e){return"static"===ee(e).position}function em(e,t){if(!G(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return _(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=I(e);if(U(e))return r;if(!G(e)){let t=en(e);for(;t&&!Q(t);){if(B(t)&&!ev(t))return t;t=en(t)}return r}let o=em(e,t);for(;o&&(n=o,K.has(O(n)))&&ev(o);)o=em(o,t);return o&&Q(o)&&ev(o)&&!Z(o)?r:o||function(e){let t=en(e);for(;G(t)&&!Q(t);){if(Z(t))return t;if(U(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=G(t),o=_(t),i="fixed"===n,l=ec(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=s(0);if(r||!r&&!i)if(("body"!==O(t)||z(o))&&(a=et(t)),r){let e=ec(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ed(o));i&&!r&&o&&(u.x=ed(o));let c=!o||r||i?s(0):ef(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=_(r),a=!!t&&U(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=s(1),d=s(0),f=G(r);if((f||!f&&!i)&&(("body"!==O(r)||z(l))&&(u=et(r)),G(r))){let e=ec(r);c=ea(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!l||f||i?s(0):ef(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-u.scrollTop*c.y+d.y+p.y}},getDocumentElement:_,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?U(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>B(e)&&"body"!==O(e)),o=null,i="fixed"===ee(e).position,l=i?en(e):e;for(;B(l)&&!Q(l);){let t=ee(l),n=Z(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||z(l)&&!n&&function e(t,n){let r=en(t);return!(r===n||!B(r)||Q(r))&&("fixed"===ee(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=en(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],s=a.reduce((e,n)=>{let r=eh(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},eh(t,u,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ei(e);return{width:t,height:n}},getScale:ea,isElement:B,isRTL:function(e){return"rtl"===ee(e).direction}};function ex(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:u,elements:s,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=k(p),w={x:n,y:r},x=v(y(o)),b=m(x),C=await u.getDimensions(d),E="y"===x,R=E?"clientHeight":"clientWidth",S=a.reference[b]+a.reference[x]-w[x]-a.floating[b],j=w[x]-a.reference[x],A=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),T=A?A[R]:0;T&&await (null==u.isElement?void 0:u.isElement(A))||(T=s.floating[R]||a.floating[b]);let M=T/2-C[b]/2-1,N=i(g[E?"top":"left"],M),D=i(g[E?"bottom":"right"],M),L=T-C[b]-D,P=T/2-C[b]/2+(S/2-j/2),O=l(N,i(P,L)),I=!c.arrow&&null!=h(o)&&P!==O&&a.reference[b]/2-(P<N?N:D)-C[b]/2<0,_=I?P<N?P-N:P-L:0;return{[x]:w[x]+_,data:{[x]:O,centerOffset:P-O-_,...I&&{alignmentOffset:_}},reset:I}}});var eC=n(7650),eE="undefined"!=typeof document?r.useLayoutEffect:function(){};function eR(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eR(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eR(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ek(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eS(e,t){let n=ek(e);return Math.round(t*n)/n}function ej(e){let t=r.useRef(e);return eE(()=>{t.current=e}),t}var eA=n(3655),eT=n(5155),eM=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eT.jsx)(eA.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eT.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eM.displayName="Arrow";var eN=n(6101),eD=n(6081),eL=n(9033),eP=n(2712),eO=n(1275),eI="Popper",[e_,eF]=(0,eD.A)(eI),[eB,eG]=e_(eI),eH=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eT.jsx)(eB,{scope:t,anchor:o,onAnchorChange:i,children:n})};eH.displayName=eI;var eW="PopperAnchor",ez=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=eG(eW,n),a=r.useRef(null),u=(0,eN.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eT.jsx)(eA.sG.div,{...i,ref:u})});ez.displayName=eW;var eK="PopperContent",[eV,eU]=e_(eK),eq=r.forwardRef((e,t)=>{var n,o,a,s,c,d,g,k;let{__scopePopper:S,side:j="bottom",sideOffset:P=0,align:O="center",alignOffset:I=0,arrowPadding:F=0,avoidCollisions:B=!0,collisionBoundary:G=[],collisionPadding:H=0,sticky:W="partial",hideWhenDetached:z=!1,updatePositionStrategy:K="optimized",onPlaced:V,...U}=e,q=eG(eK,S),[X,Y]=r.useState(null),Z=(0,eN.s)(t,e=>Y(e)),[$,J]=r.useState(null),Q=(0,eO.X)($),ee=null!=(g=null==Q?void 0:Q.width)?g:0,et=null!=(k=null==Q?void 0:Q.height)?k:0,en="number"==typeof H?H:{top:0,right:0,bottom:0,left:0,...H},eo=Array.isArray(G)?G:[G],ei=eo.length>0,ea={padding:en,boundary:eo.filter(e$),altBoundary:ei},{refs:eu,floatingStyles:es,placement:ed,isPositioned:ef,middlewareData:ep}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);eR(p,o)||h(o);let[v,m]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==E.current&&(E.current=e,m(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),b=l||v,C=a||g,E=r.useRef(null),R=r.useRef(null),k=r.useRef(d),S=null!=s,j=ej(s),T=ej(i),M=ej(c),N=r.useCallback(()=>{if(!E.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};T.current&&(e.platform=T.current),((e,t,n)=>{let r=new Map,o={platform:ew,...n},i={...o.platform,_c:r};return A(e,t,{...o,platform:i})})(E.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==M.current};D.current&&!eR(k.current,t)&&(k.current=t,eC.flushSync(()=>{f(t)}))})},[p,t,n,T,M]);eE(()=>{!1===c&&k.current.isPositioned&&(k.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let D=r.useRef(!1);eE(()=>(D.current=!0,()=>{D.current=!1}),[]),eE(()=>{if(b&&(E.current=b),C&&(R.current=C),b&&C){if(j.current)return j.current(b,C,N);N()}},[b,C,N,j,S]);let L=r.useMemo(()=>({reference:E,floating:R,setReference:w,setFloating:x}),[w,x]),P=r.useMemo(()=>({reference:b,floating:C}),[b,C]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!P.floating)return e;let t=eS(P.floating,d.x),r=eS(P.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ek(P.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,P.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:N,refs:L,elements:P,floatingStyles:O}),[d,N,L,P,O])}({strategy:"fixed",placement:j+("center"!==O?"-"+O:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=el(e),h=a||s?[...p?er(p):[],...er(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let v=p&&d?function(e,t){let n,r=null,o=_(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),a();let f=e.getBoundingClientRect(),{left:p,top:h,width:v,height:m}=f;if(c||t(),!v||!m)return;let g=u(h),y=u(o.clientWidth-(p+v)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(h+m))+"px "+-u(p)+"px",threshold:l(0,i(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||ex(f,e.getBoundingClientRect())||s(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(p,n):null,m=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?ec(e):null;return f&&function t(){let r=ec(e);y&&!ex(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==v||v(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===K})},elements:{reference:q.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await L(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}))({mainAxis:P+et,alignmentAxis:I}),B&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),d={x:n,y:r},h=await T(t,c),m=y(p(o)),g=v(m),w=d[g],x=d[m];if(a){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+h[e],r=w-h[t];w=l(n,i(w,r))}if(u){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,i(x,r))}let b=s.fn({...t,[g]:w,[m]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[g]:a,[m]:u}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===W?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=f(e,t),c={x:n,y:r},d=y(o),h=v(d),m=c[h],g=c[d],w=f(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+x.mainAxis,n=i.reference[h]+i.reference[e]-x.mainAxis;m<t?m=t:m>n&&(m=n)}if(s){var b,C;let e="y"===h?"width":"height",t=D.has(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(b=l.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(C=l.offset)?void 0:C[d])||0)-(t?x.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[h]:m,[d]:g}}}}(e),options:[e,t]}))():void 0,...ea}),B&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:c,platform:d,elements:g}=t,{mainAxis:k=!0,crossAxis:S=!0,fallbackPlacements:j,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:M="none",flipAlignment:N=!0,...D}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let L=p(a),P=y(c),O=p(c)===c,I=await (null==d.isRTL?void 0:d.isRTL(g.floating)),_=j||(O||!N?[R(c)]:function(e){let t=R(e);return[w(e),t,w(t)]}(c)),F="none"!==M;!j&&F&&_.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:x;return t?x:b;case"left":case"right":return t?C:E;default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(w)))),i}(c,N,M,I));let B=[c,..._],G=await T(t,D),H=[],W=(null==(r=u.flip)?void 0:r.overflows)||[];if(k&&H.push(G[L]),S){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=v(y(e)),i=m(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=R(l)),[l,R(l)]}(a,s,I);H.push(G[e[0]],G[e[1]])}if(W=[...W,{placement:a,overflows:H}],!H.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=B[e];if(t&&("alignment"!==S||P===y(t)||W.every(e=>e.overflows[0]>0&&y(e.placement)===P)))return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(i=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(A){case"bestFit":{let e=null==(l=W.filter(e=>{if(F){let t=y(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...ea}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a,{placement:u,rects:s,platform:c,elements:d}=t,{apply:v=()=>{},...m}=f(e,t),g=await T(t,m),w=p(u),x=h(u),b="y"===y(u),{width:C,height:E}=s.floating;"top"===w||"bottom"===w?(o=w,a=x===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(a=w,o="end"===x?"top":"bottom");let R=E-g.top-g.bottom,k=C-g.left-g.right,S=i(E-g[o],R),j=i(C-g[a],k),A=!t.middlewareData.shift,M=S,N=j;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(N=k),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(M=R),A&&!x){let e=l(g.left,0),t=l(g.right,0),n=l(g.top,0),r=l(g.bottom,0);b?N=C-2*(0!==e||0!==t?e+t:l(g.left,g.right)):M=E-2*(0!==n||0!==r?n+r:l(g.top,g.bottom))}await v({...t,availableWidth:N,availableHeight:M});let D=await c.getDimensions(d.floating);return C!==D.width||E!==D.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...ea,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),$&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:$,padding:F}),eJ({arrowWidth:ee,arrowHeight:et}),z&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=M(await T(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:N(e)}}}case"escaped":{let e=M(await T(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:N(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...ea})]}),[eh,ev]=eQ(ed),em=(0,eL.c)(V);(0,eP.N)(()=>{ef&&(null==em||em())},[ef,em]);let eg=null==(n=ep.arrow)?void 0:n.x,ey=null==(o=ep.arrow)?void 0:o.y,eM=(null==(a=ep.arrow)?void 0:a.centerOffset)!==0,[eD,eI]=r.useState();return(0,eP.N)(()=>{X&&eI(window.getComputedStyle(X).zIndex)},[X]),(0,eT.jsx)("div",{ref:eu.setFloating,"data-radix-popper-content-wrapper":"",style:{...es,transform:ef?es.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eD,"--radix-popper-transform-origin":[null==(s=ep.transformOrigin)?void 0:s.x,null==(c=ep.transformOrigin)?void 0:c.y].join(" "),...(null==(d=ep.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eT.jsx)(eV,{scope:S,placedSide:eh,onArrowChange:J,arrowX:eg,arrowY:ey,shouldHideArrow:eM,children:(0,eT.jsx)(eA.sG.div,{"data-side":eh,"data-align":ev,...U,ref:Z,style:{...U.style,animation:ef?void 0:"none"}})})})});eq.displayName=eK;var eX="PopperArrow",eY={top:"bottom",right:"left",bottom:"top",left:"right"},eZ=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eU(eX,n),i=eY[o.placedSide];return(0,eT.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eT.jsx)(eM,{...r,ref:t,style:{...r.style,display:"block"}})})});function e$(e){return null!==e}eZ.displayName=eX;var eJ=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,c=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=eQ(a),v={start:"0%",center:"50%",end:"100%"}[h],m=(null!=(i=null==(r=s.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(l=null==(o=s.arrow)?void 0:o.y)?l:0)+f/2,y="",w="";return"bottom"===p?(y=c?v:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=c?v:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=c?v:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=c?v:"".concat(g,"px")),{data:{x:y,y:w}}}});function eQ(e){let[t,n="center"]=e.split("-");return[t,n]}var e0=eH,e1=ez,e2=eq,e5=eZ},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5339:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5452:(e,t,n)=>{n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>ei,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(1285),u=n(5845),s=n(9178),c=n(7900),d=n(4378),f=n(8905),p=n(3655),h=n(2293),v=n(3795),m=n(8168),g=n(9708),y=n(5155),w="Dialog",[x,b]=(0,l.A)(w),[C,E]=x(w),R=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:l,modal:s=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.i)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:w});return(0,y.jsx)(C,{scope:t,triggerRef:c,contentRef:d,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:n})};R.displayName=w;var k="DialogTrigger",S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(k,n),a=(0,i.s)(t,l.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":U(l.open),...r,ref:a,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});S.displayName=k;var j="DialogPortal",[A,T]=x(j,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,l=E(j,t);return(0,y.jsx)(A,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:n||l.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};M.displayName=j;var N="DialogOverlay",D=r.forwardRef((e,t)=>{let n=T(N,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=E(N,e.__scopeDialog);return i.modal?(0,y.jsx)(f.C,{present:r||i.open,children:(0,y.jsx)(P,{...o,ref:t})}):null});D.displayName=N;var L=(0,g.TL)("DialogOverlay.RemoveScroll"),P=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(N,n);return(0,y.jsx)(v.A,{as:L,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":U(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),O="DialogContent",I=r.forwardRef((e,t)=>{let n=T(O,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=E(O,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||i.open,children:i.modal?(0,y.jsx)(_,{...o,ref:t}):(0,y.jsx)(F,{...o,ref:t})})});I.displayName=O;var _=r.forwardRef((e,t)=>{let n=E(O,e.__scopeDialog),l=r.useRef(null),a=(0,i.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,m.Eq)(e)},[]),(0,y.jsx)(B,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=r.forwardRef((e,t)=>{let n=E(O,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let a=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),B=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:a,...u}=e,d=E(O,n),f=r.useRef(null),p=(0,i.s)(t,f);return(0,h.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:a,children:(0,y.jsx)(s.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":U(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Z,{titleId:d.titleId}),(0,y.jsx)($,{contentRef:f,descriptionId:d.descriptionId})]})]})}),G="DialogTitle",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(G,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});H.displayName=G;var W="DialogDescription",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(W,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});z.displayName=W;var K="DialogClose",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=E(K,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function U(e){return e?"open":"closed"}V.displayName=K;var q="DialogTitleWarning",[X,Y]=(0,l.q)(q,{contentName:O,titleName:G,docsSlug:"dialog"}),Z=e=>{let{titleId:t}=e,n=Y(q),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},$=e=>{let{contentRef:t,descriptionId:n}=e,o=Y("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},J=R,Q=S,ee=M,et=D,en=I,er=H,eo=z,ei=V},5503:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(2115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},5623:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5845:(e,t,n)=>{n.d(t,{i:()=>a});var r,o=n(2115),i=n(2712),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),s=void 0!==e,c=s?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[c,o.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[s,e,a,u])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>l,q:()=>i});var r=n(2115),o=n(5155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,l=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,s=n?.[e]?.[a]||l,c=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:c,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,s=r.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},6474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6981:(e,t,n)=>{n.d(t,{C1:()=>E,bL:()=>b});var r=n(2115),o=n(6101),i=n(6081),l=n(5185),a=n(5845),u=n(5503),s=n(1275),c=n(8905),d=n(3655),f=n(5155),p="Checkbox",[h,v]=(0,i.A)(p),[m,g]=h(p);function y(e){let{__scopeCheckbox:t,checked:n,children:o,defaultChecked:i,disabled:l,form:u,name:s,onCheckedChange:c,required:d,value:h="on",internal_do_not_use_render:v}=e,[g,y]=(0,a.i)({prop:n,defaultProp:null!=i&&i,onChange:c,caller:p}),[w,x]=r.useState(null),[b,C]=r.useState(null),E=r.useRef(!1),R=!w||!!u||!!w.closest("form"),k={checked:g,disabled:l,setChecked:y,control:w,setControl:x,name:s,form:u,value:h,hasConsumerStoppedPropagationRef:E,required:d,defaultChecked:!S(i)&&i,isFormControl:R,bubbleInput:b,setBubbleInput:C};return(0,f.jsx)(m,{scope:t,...k,children:"function"==typeof v?v(k):o})}var w="CheckboxTrigger",x=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,onKeyDown:i,onClick:a,...u}=e,{control:s,value:c,disabled:p,checked:h,required:v,setControl:m,setChecked:y,hasConsumerStoppedPropagationRef:x,isFormControl:b,bubbleInput:C}=g(w,n),E=(0,o.s)(t,m),R=r.useRef(h);return r.useEffect(()=>{let e=null==s?void 0:s.form;if(e){let t=()=>y(R.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[s,y]),(0,f.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":S(h)?"mixed":h,"aria-required":v,"data-state":j(h),"data-disabled":p?"":void 0,disabled:p,value:c,...u,ref:E,onKeyDown:(0,l.m)(i,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(a,e=>{y(e=>!!S(e)||!e),C&&b&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})})});x.displayName=w;var b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:o,defaultChecked:i,required:l,disabled:a,value:u,onCheckedChange:s,form:c,...d}=e;return(0,f.jsx)(y,{__scopeCheckbox:n,checked:o,defaultChecked:i,disabled:a,required:l,onCheckedChange:s,name:r,form:c,value:u,internal_do_not_use_render:e=>{let{isFormControl:r}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(x,{...d,ref:t,__scopeCheckbox:n}),r&&(0,f.jsx)(k,{__scopeCheckbox:n})]})}})});b.displayName=p;var C="CheckboxIndicator",E=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,i=g(C,n);return(0,f.jsx)(c.C,{present:r||S(i.checked)||!0===i.checked,children:(0,f.jsx)(d.sG.span,{"data-state":j(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});E.displayName=C;var R="CheckboxBubbleInput",k=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,...i}=e,{control:l,hasConsumerStoppedPropagationRef:a,checked:c,defaultChecked:p,required:h,disabled:v,name:m,value:y,form:w,bubbleInput:x,setBubbleInput:b}=g(R,n),C=(0,o.s)(t,b),E=(0,u.Z)(c),k=(0,s.X)(l);r.useEffect(()=>{if(!x)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!a.current;if(E!==c&&e){let n=new Event("click",{bubbles:t});x.indeterminate=S(c),e.call(x,!S(c)&&c),x.dispatchEvent(n)}},[x,E,c,a]);let j=r.useRef(!S(c)&&c);return(0,f.jsx)(d.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:j.current,required:h,disabled:v,name:m,value:y,form:w,...i,tabIndex:-1,ref:C,style:{...i.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function S(e){return"indeterminate"===e}function j(e){return S(e)?"indeterminate":e?"checked":"unchecked"}k.displayName=R},7328:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function o(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function i(e,t,n){var o=r(e,t,"set");if(o.set)o.set.call(e,n);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=n}return n}n.d(t,{N:()=>f});var l,a=n(2115),u=n(6081),s=n(6101),c=n(9708),d=n(5155);function f(e){let t=e+"CollectionProvider",[n,r]=(0,u.A)(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=a.useRef(null),i=a.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};l.displayName=t;let f=e+"CollectionSlot",p=(0,c.TL)(f),h=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(f,n),l=(0,s.s)(t,o.collectionRef);return(0,d.jsx)(p,{ref:l,children:r})});h.displayName=f;let v=e+"CollectionItemSlot",m="data-radix-collection-item",g=(0,c.TL)(v),y=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,l=a.useRef(null),u=(0,s.s)(t,l),c=i(v,n);return a.useEffect(()=>(c.itemMap.set(l,{ref:l,...o}),()=>void c.itemMap.delete(l))),(0,d.jsx)(g,{...{[m]:""},ref:u,children:r})});return y.displayName=v,[{Provider:l,Slot:h,ItemSlot:y},function(t){let n=i(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var p=new WeakMap;function h(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=v(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function v(e){return e!=e||0===e?0:Math.trunc(e)}l=new WeakMap,class e extends Map{set(e,t){return p.get(this)&&(this.has(e)?o(this,l)[o(this,l).indexOf(e)]=e:o(this,l).push(e)),super.set(e,t),this}insert(e,t,n){let r,i=this.has(t),a=o(this,l).length,u=v(e),s=u>=0?u:a+u,c=s<0||s>=a?-1:s;if(c===this.size||i&&c===this.size-1||-1===c)return this.set(t,n),this;let d=this.size+ +!i;u<0&&s++;let f=[...o(this,l)],p=!1;for(let e=s;e<d;e++)if(s===e){let o=f[e];f[e]===t&&(o=f[e+1]),i&&this.delete(t),r=this.get(o),this.set(t,n)}else{p||f[e-1]!==t||(p=!0);let n=f[p?e:e-1],o=r;r=this.get(n),this.delete(n),this.set(n,o)}return this}with(t,n,r){let o=new e(this);return o.insert(t,n,r),o}before(e){let t=o(this,l).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,n){let r=o(this,l).indexOf(e);return -1===r?this:this.insert(r,t,n)}after(e){let t=o(this,l).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,n){let r=o(this,l).indexOf(e);return -1===r?this:this.insert(r+1,t,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return i(this,l,[]),super.clear()}delete(e){let t=super.delete(e);return t&&o(this,l).splice(o(this,l).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=h(o(this,l),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=h(o(this,l),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return o(this,l).indexOf(e)}keyAt(e){return h(o(this,l),e)}from(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.at(r)}keyFrom(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.keyAt(r)}find(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return r;n++}}findIndex(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return n;n++}return -1}filter(t,n){let r=[],o=0;for(let e of this)Reflect.apply(t,n,[e,o,this])&&r.push(e),o++;return new e(r)}map(t,n){let r=[],o=0;for(let e of this)r.push([e[0],Reflect.apply(t,n,[e,o,this])]),o++;return new e(r)}reduce(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=0,l=null!=o?o:this.at(0);for(let e of this)l=0===i&&1===t.length?e:Reflect.apply(r,this,[l,e,i,this]),i++;return l}reduceRight(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=null!=o?o:this.at(-1);for(let e=this.size-1;e>=0;e--){let n=this.at(e);i=e===this.size-1&&1===t.length?n:Reflect.apply(r,this,[i,n,e,this])}return i}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let n=this.keyAt(e),r=this.get(n);t.set(n,r)}return t}toSpliced(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let o=[...this.entries()];return o.splice(...n),new e(o)}slice(t,n){let r=new e,o=this.size-1;if(void 0===t)return r;t<0&&(t+=this.size),void 0!==n&&n>0&&(o=n-1);for(let e=t;e<=o;e++){let t=this.keyAt(e),n=this.get(t);r.set(t,n)}return r}every(e,t){let n=0;for(let r of this){if(!Reflect.apply(e,t,[r,n,this]))return!1;n++}return!0}some(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return!0;n++}return!1}constructor(e){super(e),function(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}(this,l,{writable:!0,value:void 0}),i(this,l,[...super.keys()]),p.set(this,!0)}}},7340:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},7900:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(2115),o=n(6101),i=n(3655),l=n(9033),a=n(5155),u="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...y}=e,[w,x]=r.useState(null),b=(0,l.c)(m),C=(0,l.c)(g),E=r.useRef(null),R=(0,o.s)(t,e=>x(e)),k=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(k.paused||!w)return;let t=e.target;w.contains(t)?E.current=t:h(E.current,{select:!0})},t=function(e){if(k.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||h(E.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,k.paused]),r.useEffect(()=>{if(w){v.add(k);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,c);w.addEventListener(u,b),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,b),setTimeout(()=>{let t=new CustomEvent(s,c);w.addEventListener(s,C),w.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),w.removeEventListener(s,C),v.remove(k)},0)}}},[w,b,C,k]);let S=r.useCallback(e=>{if(!n&&!d||k.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,k.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...y,ref:R,onKeyDown:S})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var v=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=m(e,t)).unshift(t)},remove(t){var n;null==(n=(e=m(e,t))[0])||n.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},7924:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=new WeakMap,o=new WeakMap,i={},l=0,a=function(e){return e&&(e.host||a(e.parentNode))},u=function(e,t,n,u){var s=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=a(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var c=i[n],d=[],f=new Set,p=new Set(s),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};s.forEach(h);var v=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))v(e);else try{var t=e.getAttribute(u),i=null!==t&&"false"!==t,l=(r.get(e)||0)+1,a=(c.get(e)||0)+1;r.set(e,l),c.set(e,a),d.push(e),1===l&&i&&o.set(e,!0),1===a&&e.setAttribute(n,"true"),i||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),f.clear(),l++,function(){d.forEach(function(e){var t=r.get(e)-1,i=c.get(e)-1;r.set(e,t),c.set(e,i),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),i||e.removeAttribute(n)}),--l||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),u(r,o,n,"aria-hidden")):function(){return null}}},8698:(e,t,n)=>{n.d(t,{H_:()=>e6,UC:()=>e5,YJ:()=>e3,q7:()=>e8,VF:()=>te,JU:()=>e9,ZL:()=>e2,z6:()=>e7,hN:()=>e4,bL:()=>e0,wv:()=>tt,Pb:()=>tn,G5:()=>to,ZP:()=>tr,l9:()=>e1});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(5845),u=n(3655),s=n(7328),c=n(4315),d=n(9178),f=n(2293),p=n(7900),h=n(1285),v=n(5152),m=n(4378),g=n(8905),y=n(9196),w=n(9708),x=n(9033),b=n(8168),C=n(3795),E=n(5155),R=["Enter"," "],k=["ArrowUp","PageDown","End"],S=["ArrowDown","PageUp","Home",...k],j={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},A={ltr:["ArrowLeft"],rtl:["ArrowRight"]},T="Menu",[M,N,D]=(0,s.N)(T),[L,P]=(0,l.A)(T,[D,v.Bk,y.RG]),O=(0,v.Bk)(),I=(0,y.RG)(),[_,F]=L(T),[B,G]=L(T),H=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:l,modal:a=!0}=e,u=O(t),[s,d]=r.useState(null),f=r.useRef(!1),p=(0,x.c)(l),h=(0,c.jH)(i);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,E.jsx)(v.bL,{...u,children:(0,E.jsx)(_,{scope:t,open:n,onOpenChange:p,content:s,onContentChange:d,children:(0,E.jsx)(B,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:a,children:o})})})};H.displayName=T;var W=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=O(n);return(0,E.jsx)(v.Mz,{...o,...r,ref:t})});W.displayName="MenuAnchor";var z="MenuPortal",[K,V]=L(z,{forceMount:void 0}),U=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=F(z,t);return(0,E.jsx)(K,{scope:t,forceMount:n,children:(0,E.jsx)(g.C,{present:n||i.open,children:(0,E.jsx)(m.Z,{asChild:!0,container:o,children:r})})})};U.displayName=z;var q="MenuContent",[X,Y]=L(q),Z=r.forwardRef((e,t)=>{let n=V(q,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=F(q,e.__scopeMenu),l=G(q,e.__scopeMenu);return(0,E.jsx)(M.Provider,{scope:e.__scopeMenu,children:(0,E.jsx)(g.C,{present:r||i.open,children:(0,E.jsx)(M.Slot,{scope:e.__scopeMenu,children:l.modal?(0,E.jsx)($,{...o,ref:t}):(0,E.jsx)(J,{...o,ref:t})})})})}),$=r.forwardRef((e,t)=>{let n=F(q,e.__scopeMenu),l=r.useRef(null),a=(0,i.s)(t,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,b.Eq)(e)},[]),(0,E.jsx)(ee,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),J=r.forwardRef((e,t)=>{let n=F(q,e.__scopeMenu);return(0,E.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Q=(0,w.TL)("MenuContent.ScrollLock"),ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:a,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:h,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:x,onDismiss:b,disableOutsideScroll:R,...j}=e,A=F(q,n),T=G(q,n),M=O(n),D=I(n),L=N(n),[P,_]=r.useState(null),B=r.useRef(null),H=(0,i.s)(t,B,A.onContentChange),W=r.useRef(0),z=r.useRef(""),K=r.useRef(0),V=r.useRef(null),U=r.useRef("right"),Y=r.useRef(0),Z=R?C.A:r.Fragment;r.useEffect(()=>()=>window.clearTimeout(W.current),[]),(0,f.Oh)();let $=r.useCallback(e=>{var t,n;return U.current===(null==(t=V.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,s=l.y,c=a.x,d=a.y;s>r!=d>r&&n<(c-u)*(r-s)/(d-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=V.current)?void 0:n.area)},[]);return(0,E.jsx)(X,{scope:n,searchRef:z,onItemEnter:r.useCallback(e=>{$(e)&&e.preventDefault()},[$]),onItemLeave:r.useCallback(e=>{var t;$(e)||(null==(t=B.current)||t.focus(),_(null))},[$]),onTriggerLeave:r.useCallback(e=>{$(e)&&e.preventDefault()},[$]),pointerGraceTimerRef:K,onPointerGraceIntentChange:r.useCallback(e=>{V.current=e},[]),children:(0,E.jsx)(Z,{...R?{as:Q,allowPinchZoom:!0}:void 0,children:(0,E.jsx)(p.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,o.m)(u,e=>{var t;e.preventDefault(),null==(t=B.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,E.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:x,onDismiss:b,children:(0,E.jsx)(y.bL,{asChild:!0,...D,dir:T.dir,orientation:"vertical",loop:l,currentTabStopId:P,onCurrentTabStopIdChange:_,onEntryFocus:(0,o.m)(h,e=>{T.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,E.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":eA(A.open),"data-radix-menu-content":"",dir:T.dir,...M,...j,ref:H,style:{outline:"none",...j.style},onKeyDown:(0,o.m)(j.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&(e=>{var t,n;let r=z.current+e,o=L().filter(e=>!e.disabled),i=document.activeElement,l=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,a=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,l=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let a=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}(o.map(e=>e.textValue),r,l),u=null==(n=o.find(e=>e.textValue===a))?void 0:n.ref.current;!function e(t){z.current=t,window.clearTimeout(W.current),""!==t&&(W.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())})(e.key));let o=B.current;if(e.target!==o||!S.includes(e.key))return;e.preventDefault();let i=L().filter(e=>!e.disabled).map(e=>e.ref.current);k.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(W.current),z.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eN(e=>{let t=e.target,n=Y.current!==e.clientX;e.currentTarget.contains(t)&&n&&(U.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});Z.displayName=q;var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,E.jsx)(u.sG.div,{role:"group",...r,ref:t})});et.displayName="MenuGroup";var en=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,E.jsx)(u.sG.div,{...r,ref:t})});en.displayName="MenuLabel";var er="MenuItem",eo="menu.itemSelect",ei=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:l,...a}=e,s=r.useRef(null),c=G(er,e.__scopeMenu),d=Y(er,e.__scopeMenu),f=(0,i.s)(t,s),p=r.useRef(!1);return(0,E.jsx)(el,{...a,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==l?void 0:l(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ei.displayName=er;var el=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:l=!1,textValue:a,...s}=e,c=Y(er,n),d=I(n),f=r.useRef(null),p=(0,i.s)(t,f),[h,v]=r.useState(!1),[m,g]=r.useState("");return r.useEffect(()=>{let e=f.current;if(e){var t;g((null!=(t=e.textContent)?t:"").trim())}},[s.children]),(0,E.jsx)(M.ItemSlot,{scope:n,disabled:l,textValue:null!=a?a:m,children:(0,E.jsx)(y.q7,{asChild:!0,...d,focusable:!l,children:(0,E.jsx)(u.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...s,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eN(e=>{l?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eN(e=>c.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),ea=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,E.jsx)(ev,{scope:e.__scopeMenu,checked:n,children:(0,E.jsx)(ei,{role:"menuitemcheckbox","aria-checked":eT(n)?"mixed":n,...i,ref:t,"data-state":eM(n),onSelect:(0,o.m)(i.onSelect,()=>null==r?void 0:r(!!eT(n)||!n),{checkForDefaultPrevented:!1})})})});ea.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[es,ec]=L(eu,{value:void 0,onValueChange:()=>{}}),ed=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,x.c)(r);return(0,E.jsx)(es,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,E.jsx)(et,{...o,ref:t})})});ed.displayName=eu;var ef="MenuRadioItem",ep=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=ec(ef,e.__scopeMenu),l=n===i.value;return(0,E.jsx)(ev,{scope:e.__scopeMenu,checked:l,children:(0,E.jsx)(ei,{role:"menuitemradio","aria-checked":l,...r,ref:t,"data-state":eM(l),onSelect:(0,o.m)(r.onSelect,()=>{var e;return null==(e=i.onValueChange)?void 0:e.call(i,n)},{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var eh="MenuItemIndicator",[ev,em]=L(eh,{checked:!1}),eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=em(eh,n);return(0,E.jsx)(g.C,{present:r||eT(i.checked)||!0===i.checked,children:(0,E.jsx)(u.sG.span,{...o,ref:t,"data-state":eM(i.checked)})})});eg.displayName=eh;var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,E.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ey.displayName="MenuSeparator";var ew=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=O(n);return(0,E.jsx)(v.i3,{...o,...r,ref:t})});ew.displayName="MenuArrow";var ex="MenuSub",[eb,eC]=L(ex),eE=e=>{let{__scopeMenu:t,children:n,open:o=!1,onOpenChange:i}=e,l=F(ex,t),a=O(t),[u,s]=r.useState(null),[c,d]=r.useState(null),f=(0,x.c)(i);return r.useEffect(()=>(!1===l.open&&f(!1),()=>f(!1)),[l.open,f]),(0,E.jsx)(v.bL,{...a,children:(0,E.jsx)(_,{scope:t,open:o,onOpenChange:f,content:c,onContentChange:d,children:(0,E.jsx)(eb,{scope:t,contentId:(0,h.B)(),triggerId:(0,h.B)(),trigger:u,onTriggerChange:s,children:n})})})};eE.displayName=ex;var eR="MenuSubTrigger",ek=r.forwardRef((e,t)=>{let n=F(eR,e.__scopeMenu),l=G(eR,e.__scopeMenu),a=eC(eR,e.__scopeMenu),u=Y(eR,e.__scopeMenu),s=r.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,E.jsx)(W,{asChild:!0,...f,children:(0,E.jsx)(el,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":a.contentId,"data-state":eA(n.open),...e,ref:(0,i.t)(t,a.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eN(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eN(e=>{var t,r;p();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,i="right"===t,l=o[i?"left":"right"],a=o[i?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:l,y:o.top},{x:a,y:o.top},{x:a,y:o.bottom},{x:l,y:o.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;if(!e.disabled&&(!r||" "!==t.key)&&j[l.dir].includes(t.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),t.preventDefault()}})})})});ek.displayName=eR;var eS="MenuSubContent",ej=r.forwardRef((e,t)=>{let n=V(q,e.__scopeMenu),{forceMount:l=n.forceMount,...a}=e,u=F(q,e.__scopeMenu),s=G(q,e.__scopeMenu),c=eC(eS,e.__scopeMenu),d=r.useRef(null),f=(0,i.s)(t,d);return(0,E.jsx)(M.Provider,{scope:e.__scopeMenu,children:(0,E.jsx)(g.C,{present:l||u.open,children:(0,E.jsx)(M.Slot,{scope:e.__scopeMenu,children:(0,E.jsx)(ee,{id:c.contentId,"aria-labelledby":c.triggerId,...a,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;s.isUsingKeyboardRef.current&&(null==(t=d.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=A[s.dir].includes(e.key);if(t&&n){var r;u.onOpenChange(!1),null==(r=c.trigger)||r.focus(),e.preventDefault()}})})})})})});function eA(e){return e?"open":"closed"}function eT(e){return"indeterminate"===e}function eM(e){return eT(e)?"indeterminate":e?"checked":"unchecked"}function eN(e){return t=>"mouse"===t.pointerType?e(t):void 0}ej.displayName=eS;var eD="DropdownMenu",[eL,eP]=(0,l.A)(eD,[P]),eO=P(),[eI,e_]=eL(eD),eF=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:l,onOpenChange:u,modal:s=!0}=e,c=eO(t),d=r.useRef(null),[f,p]=(0,a.i)({prop:i,defaultProp:null!=l&&l,onChange:u,caller:eD});return(0,E.jsx)(eI,{scope:t,triggerId:(0,h.B)(),triggerRef:d,contentId:(0,h.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,E.jsx)(H,{...c,open:f,onOpenChange:p,dir:o,modal:s,children:n})})};eF.displayName=eD;var eB="DropdownMenuTrigger",eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...l}=e,a=e_(eB,n),s=eO(n);return(0,E.jsx)(W,{asChild:!0,...s,children:(0,E.jsx)(u.sG.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...l,ref:(0,i.t)(t,a.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eG.displayName=eB;var eH=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eO(t);return(0,E.jsx)(U,{...r,...n})};eH.displayName="DropdownMenuPortal";var eW="DropdownMenuContent",ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,l=e_(eW,n),a=eO(n),u=r.useRef(!1);return(0,E.jsx)(Z,{id:l.contentId,"aria-labelledby":l.triggerId,...a,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;u.current||null==(t=l.triggerRef.current)||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!l.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ez.displayName=eW;var eK=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,E.jsx)(et,{...o,...r,ref:t})});eK.displayName="DropdownMenuGroup";var eV=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,E.jsx)(en,{...o,...r,ref:t})});eV.displayName="DropdownMenuLabel";var eU=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,E.jsx)(ei,{...o,...r,ref:t})});eU.displayName="DropdownMenuItem";var eq=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,E.jsx)(ea,{...o,...r,ref:t})});eq.displayName="DropdownMenuCheckboxItem";var eX=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,E.jsx)(ed,{...o,...r,ref:t})});eX.displayName="DropdownMenuRadioGroup";var eY=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,E.jsx)(ep,{...o,...r,ref:t})});eY.displayName="DropdownMenuRadioItem";var eZ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,E.jsx)(eg,{...o,...r,ref:t})});eZ.displayName="DropdownMenuItemIndicator";var e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,E.jsx)(ey,{...o,...r,ref:t})});e$.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,E.jsx)(ew,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var eJ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,E.jsx)(ek,{...o,...r,ref:t})});eJ.displayName="DropdownMenuSubTrigger";var eQ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,E.jsx)(ej,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eQ.displayName="DropdownMenuSubContent";var e0=eF,e1=eG,e2=eH,e5=ez,e3=eK,e9=eV,e8=eU,e6=eq,e7=eX,e4=eY,te=eZ,tt=e$,tn=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:i}=e,l=eO(t),[u,s]=(0,a.i)({prop:r,defaultProp:null!=i&&i,onChange:o,caller:"DropdownMenuSub"});return(0,E.jsx)(eE,{...l,open:u,onOpenChange:s,children:n})},tr=eJ,to=eQ},8905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(2115),o=n(6101),i=n(2712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),u=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(u.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=c.current,o=a(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=a(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=a(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),s=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||l.isPresent?r.cloneElement(u,{ref:s}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9178:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(2115),i=n(5185),l=n(3655),a=n(6101),u=n(9033),s=n(5155),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:v=!1,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:x,...b}=e,C=o.useContext(d),[E,R]=o.useState(null),k=null!=(f=null==E?void 0:E.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,S]=o.useState({}),j=(0,a.s)(t,e=>R(e)),A=Array.from(C.layers),[T]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),M=A.indexOf(T),N=E?A.indexOf(E):-1,D=C.layersWithOutsidePointerEventsDisabled.size>0,L=N>=M,P=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));L&&!n&&(null==g||g(e),null==w||w(e),e.defaultPrevented||null==x||x())},k),O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...C.branches].some(e=>e.contains(t))&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==x||x())},k);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===C.layers.size-1&&(null==m||m(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},k),o.useEffect(()=>{if(E)return v&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=k.body.style.pointerEvents,k.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(E)),C.layers.add(E),p(),()=>{v&&1===C.layersWithOutsidePointerEventsDisabled.size&&(k.body.style.pointerEvents=r)}},[E,k,v,C]),o.useEffect(()=>()=>{E&&(C.layers.delete(E),C.layersWithOutsidePointerEventsDisabled.delete(E),p())},[E,C]),o.useEffect(()=>{let e=()=>S({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,s.jsx)(l.sG.div,{...b,ref:j,style:{pointerEvents:D?L?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,P.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},9196:(e,t,n)=>{n.d(t,{RG:()=>b,bL:()=>M,q7:()=>N});var r=n(2115),o=n(5185),i=n(7328),l=n(6101),a=n(6081),u=n(1285),s=n(3655),c=n(9033),d=n(5845),f=n(4315),p=n(5155),h="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[g,y,w]=(0,i.N)(m),[x,b]=(0,a.A)(m,[w]),[C,E]=x(m),R=r.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(k,{...e,ref:t})})}));R.displayName=m;var k=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:a=!1,dir:u,currentTabStopId:g,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:x,onEntryFocus:b,preventScrollOnEntryFocus:E=!1,...R}=e,k=r.useRef(null),S=(0,l.s)(t,k),j=(0,f.jH)(u),[A,M]=(0,d.i)({prop:g,defaultProp:null!=w?w:null,onChange:x,caller:m}),[N,D]=r.useState(!1),L=(0,c.c)(b),P=y(n),O=r.useRef(!1),[I,_]=r.useState(0);return r.useEffect(()=>{let e=k.current;if(e)return e.addEventListener(h,L),()=>e.removeEventListener(h,L)},[L]),(0,p.jsx)(C,{scope:n,orientation:i,dir:j,loop:a,currentTabStopId:A,onItemFocus:r.useCallback(e=>M(e),[M]),onItemShiftTab:r.useCallback(()=>D(!0),[]),onFocusableItemAdd:r.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>_(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:N||0===I?-1:0,"data-orientation":i,...R,ref:S,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!N){let t=new CustomEvent(h,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),E)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>D(!1))})})}),S="RovingFocusGroupItem",j=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:l=!1,tabStopId:a,children:c,...d}=e,f=(0,u.B)(),h=a||f,v=E(S,n),m=v.currentTabStopId===h,w=y(n),{onFocusableItemAdd:x,onFocusableItemRemove:b,currentTabStopId:C}=v;return r.useEffect(()=>{if(i)return x(),()=>b()},[i,x,b]),(0,p.jsx)(g.ItemSlot,{scope:n,id:h,focusable:i,active:l,children:(0,p.jsx)(s.sG.span,{tabIndex:m?0:-1,"data-orientation":v.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?v.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(h)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return A[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=v.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>T(n))}}),children:"function"==typeof c?c({isCurrentTabStop:m,hasTabStop:null!=C}):c})})});j.displayName=S;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var M=R,N=j},9323:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("file-x",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]])},9428:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},9613:(e,t,n)=>{n.d(t,{Kq:()=>G,UC:()=>z,bL:()=>H,l9:()=>W});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(9178),u=n(1285),s=n(5152),c=(n(4378),n(8905)),d=n(3655),f=n(9708),p=n(5845),h=n(2564),v=n(5155),[m,g]=(0,l.A)("Tooltip",[s.Bk]),y=(0,s.Bk)(),w="TooltipProvider",x="tooltip.open",[b,C]=m(w),E=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,a=r.useRef(!0),u=r.useRef(!1),s=r.useRef(0);return r.useEffect(()=>{let e=s.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(b,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(s.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:l})};E.displayName=w;var R="Tooltip",[k,S]=m(R),j=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:c}=e,d=C(R,e.__scopeTooltip),f=y(t),[h,m]=r.useState(null),g=(0,u.B)(),w=r.useRef(0),b=null!=a?a:d.disableHoverableContent,E=null!=c?c:d.delayDuration,S=r.useRef(!1),[j,A]=(0,p.i)({prop:o,defaultProp:null!=i&&i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(x))):d.onClose(),null==l||l(e)},caller:R}),T=r.useMemo(()=>j?S.current?"delayed-open":"instant-open":"closed",[j]),M=r.useCallback(()=>{window.clearTimeout(w.current),w.current=0,S.current=!1,A(!0)},[A]),N=r.useCallback(()=>{window.clearTimeout(w.current),w.current=0,A(!1)},[A]),D=r.useCallback(()=>{window.clearTimeout(w.current),w.current=window.setTimeout(()=>{S.current=!0,A(!0),w.current=0},E)},[E,A]);return r.useEffect(()=>()=>{w.current&&(window.clearTimeout(w.current),w.current=0)},[]),(0,v.jsx)(s.bL,{...f,children:(0,v.jsx)(k,{scope:t,contentId:g,open:j,stateAttribute:T,trigger:h,onTriggerChange:m,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayedRef.current?D():M()},[d.isOpenDelayedRef,D,M]),onTriggerLeave:r.useCallback(()=>{b?N():(window.clearTimeout(w.current),w.current=0)},[N,b]),onOpen:M,onClose:N,disableHoverableContent:b,children:n})})};j.displayName=R;var A="TooltipTrigger",T=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...l}=e,a=S(A,n),u=C(A,n),c=y(n),f=r.useRef(null),p=(0,i.s)(t,f,a.onTriggerChange),h=r.useRef(!1),m=r.useRef(!1),g=r.useCallback(()=>h.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,v.jsx)(s.Mz,{asChild:!0,...c,children:(0,v.jsx)(d.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(m.current||u.isPointerInTransitRef.current||(a.onTriggerEnter(),m.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),m.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),h.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});T.displayName=A;var[M,N]=m("TooltipPortal",{forceMount:void 0}),D="TooltipContent",L=r.forwardRef((e,t)=>{let n=N(D,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=S(D,e.__scopeTooltip);return(0,v.jsx)(c.C,{present:r||l.open,children:l.disableHoverableContent?(0,v.jsx)(F,{side:o,...i,ref:t}):(0,v.jsx)(P,{side:o,...i,ref:t})})}),P=r.forwardRef((e,t)=>{let n=S(D,e.__scopeTooltip),o=C(D,e.__scopeTooltip),l=r.useRef(null),a=(0,i.s)(t,l),[u,s]=r.useState(null),{trigger:c,onClose:d}=n,f=l.current,{onPointerInTransitChange:p}=o,h=r.useCallback(()=>{s(null),p(!1)},[p]),m=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());s(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(c&&f){let e=e=>m(e,f),t=e=>m(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,m,h]),r.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,s=l.y,c=a.x,d=a.y;s>r!=d>r&&n<(c-u)*(r-s)/(d-s)+u&&(o=!o)}return o}(n,u);r?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,u,d,h]),(0,v.jsx)(F,{...e,ref:a})}),[O,I]=m(R,{isInside:!1}),_=(0,f.Dc)("TooltipContent"),F=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:u,...c}=e,d=S(D,n),f=y(n),{onClose:p}=d;return r.useEffect(()=>(document.addEventListener(x,p),()=>document.removeEventListener(x,p)),[p]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,p]),(0,v.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,v.jsxs)(s.UC,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(_,{children:o}),(0,v.jsx)(O,{scope:n,isInside:!0,children:(0,v.jsx)(h.bL,{id:d.contentId,role:"tooltip",children:i||o})})]})})});L.displayName=D;var B="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=y(n);return I(B,n).isInside?null:(0,v.jsx)(s.i3,{...o,...r,ref:t})}).displayName=B;var G=E,H=j,W=T,z=L}}]);