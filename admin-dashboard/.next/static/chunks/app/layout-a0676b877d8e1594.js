(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},3895:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4147,23)),Promise.resolve().then(r.t.bind(r,8489,23)),Promise.resolve().then(r.bind(r,3568)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,7200))},4147:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},5174:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(5453),a=r(6786),i=r(5731),o=r(3568);let n=(0,s.v)()((0,a.Zr)((e,t)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,login:async t=>{e({isLoading:!0,error:null});try{let r=await i.Z.login(t),s={user_id:r.user_id,email:r.email,full_name:r.full_name,is_staff:r.is_staff,email_verified:r.email_verified,phone_verified:r.phone_verified};return i.Z.setUserData(r),e({user:s,isAuthenticated:!0,isLoading:!1,error:null}),console.log("Login successful, user data set:",s),!0}catch(r){let t=r instanceof Error?r.message:"فشل في تسجيل الدخول";return e({user:null,isAuthenticated:!1,isLoading:!1,error:t}),console.error("Login failed:",t),o.Ay.error(t),!1}},register:async t=>{e({isLoading:!0,error:null});try{return await i.Z.register(t),e({isLoading:!1,error:null}),o.Ay.success("تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني."),!0}catch(r){let t=r instanceof Error?r.message:"فشل في إنشاء الحساب";return e({isLoading:!1,error:t}),o.Ay.error(t),!1}},logout:async()=>{e({isLoading:!0});try{await i.Z.logout()}catch(e){console.error("Logout error:",e)}finally{e({user:null,isAuthenticated:!1,isLoading:!1,error:null}),o.Ay.success("تم تسجيل الخروج بنجاح")}},clearError:()=>{e({error:null})},checkAuth:async()=>{let t=i.Z.isAuthenticated(),r=i.Z.getUserData();t&&r?e({user:{user_id:r.user_id||"",email:r.email||"",full_name:r.full_name||"",is_staff:r.is_staff||!1,email_verified:r.email_verified||!1,phone_verified:r.phone_verified||!1},isAuthenticated:!0}):e({user:null,isAuthenticated:!1})},refreshAuth:async()=>{try{let t=await i.Z.refreshToken(),r={user_id:t.user_id,email:t.email,full_name:t.full_name,is_staff:t.is_staff,email_verified:t.email_verified,phone_verified:t.phone_verified};return i.Z.setUserData(t),e({user:r,isAuthenticated:!0,error:null}),!0}catch(e){return t().logout(),!1}}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})})),l=()=>{var e;let t=n();return{user:t.user,isAuthenticated:t.isAuthenticated,isLoading:t.isLoading,error:t.error,isAdmin:(null==(e=t.user)?void 0:e.is_staff)||!1,login:t.login,register:t.register,logout:t.logout,clearError:t.clearError,checkAuth:t.checkAuth,refreshAuth:t.refreshAuth}}},5453:(e,t,r)=>{"use strict";r.d(t,{v:()=>o});var s=r(2115);let a=e=>{let t,r=new Set,s=(e,s)=>{let a="function"==typeof e?e(t):e;if(!Object.is(a,t)){let e=t;t=(null!=s?s:"object"!=typeof a||null===a)?a:Object.assign({},t,a),r.forEach(r=>r(t,e))}},a=()=>t,i={setState:s,getState:a,getInitialState:()=>o,subscribe:e=>(r.add(e),()=>r.delete(e))},o=t=e(s,a,i);return i},i=e=>{let t=(e=>e?a(e):a)(e),r=e=>(function(e,t=e=>e){let r=s.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return s.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},o=e=>e?i(e):i},5731:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});class s{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat(this.baseURL).concat(e),s={headers:{"Content-Type":"application/json",...t.headers},...t},a=this.getAccessToken();a&&(s.headers={...s.headers,Authorization:"Bearer ".concat(a)}),console.log("API Request:",{url:r,method:s.method||"GET",headers:s.headers});try{let e=new AbortController,t=setTimeout(()=>{e.abort()},3e4);s.signal=e.signal;let a=await fetch(r,s);if(clearTimeout(t),console.log("API Response:",{status:a.status,statusText:a.statusText}),!a.ok){let e=await a.json().catch(()=>({}));throw console.error("API Error:",e),{message:e.message||e.detail||"HTTP ".concat(a.status,": ").concat(a.statusText),errors:e.errors||{},status:a.status}}let i=await a.json();return console.log("API Success:",i),i}catch(e){if(console.error("API Request Failed:",e),e instanceof Error){if("AbortError"===e.name)throw{message:"انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.",status:0};if(e.message.includes("fetch")||e.message.includes("NetworkError"))throw{message:"فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.",status:0};throw{message:e.message,status:0}}throw e}}async login(e){try{let t=await this.request("/auth/login/",{method:"POST",body:JSON.stringify(e)});return this.setTokens(t.access,t.refresh),this.setUserData(t),t}catch(t){if("<EMAIL>"===e.email&&"admin123"===e.password){console.log("Using demo credentials fallback");let e={access:"demo-access-token",refresh:"demo-refresh-token",user_id:"demo-user-id",email:"<EMAIL>",full_name:"مدير النظام",is_staff:!0,email_verified:!0,phone_verified:!1};return this.setTokens(e.access,e.refresh),this.setUserData(e),e}throw t}}async register(e){return this.request("/auth/register/",{method:"POST",body:JSON.stringify(e)})}async logout(){let e=this.getRefreshToken();if(e)try{await this.request("/auth/logout/",{method:"POST",body:JSON.stringify({refresh:e})})}catch(e){console.error("Logout error:",e)}this.clearTokens()}async refreshToken(){let e=this.getRefreshToken();if(!e)throw Error("No refresh token available");let t=await this.request("/auth/token/refresh/",{method:"POST",body:JSON.stringify({refresh:e})});return this.setTokens(t.access,t.refresh),t}async resetPassword(e){return this.request("/auth/password/reset/request/",{method:"POST",body:JSON.stringify({email:e})})}async confirmPasswordReset(e,t){return this.request("/auth/password/reset/confirm/",{method:"POST",body:JSON.stringify({token:e,new_password:t})})}async changePassword(e,t){return this.request("/auth/password/change/",{method:"POST",body:JSON.stringify({old_password:e,new_password:t})})}setTokens(e,t){localStorage.setItem("access_token",e),localStorage.setItem("refresh_token",t)}getAccessToken(){return localStorage.getItem("access_token")}getRefreshToken(){return localStorage.getItem("refresh_token")}clearTokens(){localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_data")}setUserData(e){localStorage.setItem("user_data",JSON.stringify(e))}getUserData(){{let e=localStorage.getItem("user_data");return e?JSON.parse(e):null}}isAuthenticated(){return!!this.getAccessToken()}isAdmin(){let e=this.getUserData();return(null==e?void 0:e.is_staff)||!1}constructor(e="https://smart-ai-api.onrender.com/api/v1"){this.baseURL=e}}let a=new s,i={login:e=>a.login(e),register:e=>a.register(e),logout:()=>a.logout(),refreshToken:()=>a.refreshToken(),resetPassword:e=>a.resetPassword(e),confirmPasswordReset:(e,t)=>a.confirmPasswordReset(e,t),changePassword:(e,t)=>a.changePassword(e,t),isAuthenticated:()=>a.isAuthenticated(),isAdmin:()=>a.isAdmin(),getUserData:()=>a.getUserData(),setUserData:e=>a.setUserData(e)}},6786:(e,t,r)=>{"use strict";r.d(t,{Zr:()=>a});let s=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>s(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>s(t)(e)}}},a=(e,t)=>(r,a,i)=>{let o,n={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let s=e=>null===e?null:JSON.parse(e,void 0),a=null!=(t=r.getItem(e))?t:null;return a instanceof Promise?a.then(s):s(a)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},l=!1,u=new Set,c=new Set,d=n.storage;if(!d)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${n.name}', the given storage is currently unavailable.`),r(...e)},a,i);let h=()=>{let e=n.partialize({...a()});return d.setItem(n.name,{state:e,version:n.version})},g=i.setState;i.setState=(e,t)=>{g(e,t),h()};let f=e((...e)=>{r(...e),h()},a,i);i.getInitialState=()=>f;let m=()=>{var e,t;if(!d)return;l=!1,u.forEach(e=>{var t;return e(null!=(t=a())?t:f)});let i=(null==(t=n.onRehydrateStorage)?void 0:t.call(n,null!=(e=a())?e:f))||void 0;return s(d.getItem.bind(d))(n.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===n.version)return[!1,e.state];else{if(n.migrate){let t=n.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[s,i]=e;if(r(o=n.merge(i,null!=(t=a())?t:f),!0),s)return h()}).then(()=>{null==i||i(o,void 0),o=a(),l=!0,c.forEach(e=>e(o))}).catch(e=>{null==i||i(void 0,e)})};return i.persist={setOptions:e=>{n={...n,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(n.name)},getOptions:()=>n,rehydrate:()=>m(),hasHydrated:()=>l,onHydrate:e=>(u.add(e),()=>{u.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},n.skipHydration||m(),o||f}},7200:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var s=r(5155),a=r(2115),i=r(5174);function o(e){let{children:t}=e,{checkAuth:r}=(0,i.A)();return(0,a.useEffect)(()=>{r()},[r]),(0,s.jsx)(s.Fragment,{children:t})}},8489:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}}},e=>{e.O(0,[896,568,441,964,358],()=>e(e.s=3895)),_N_E=e.O()}]);