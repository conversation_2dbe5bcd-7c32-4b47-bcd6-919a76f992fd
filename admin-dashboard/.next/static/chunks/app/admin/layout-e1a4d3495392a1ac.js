(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[581],{285:(e,s,t)=>{"use strict";t.d(s,{$:()=>o,r:()=>l});var r=t(5155);t(2115);var a=t(9708),i=t(2085),n=t(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:s,variant:t,size:i,asChild:o=!1,...c}=e,d=o?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:i,className:s})),...c})}},381:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},873:(e,s,t)=>{Promise.resolve().then(t.bind(t,7537)),Promise.resolve().then(t.bind(t,6117))},3227:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},4416:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5174:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var r=t(5453),a=t(6786),i=t(5731),n=t(3568);let l=(0,r.v)()((0,a.Zr)((e,s)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,login:async s=>{e({isLoading:!0,error:null});try{let t=await i.Z.login(s),r={user_id:t.user_id,email:t.email,full_name:t.full_name,is_staff:t.is_staff,email_verified:t.email_verified,phone_verified:t.phone_verified};return i.Z.setUserData(t),e({user:r,isAuthenticated:!0,isLoading:!1,error:null}),console.log("Login successful, user data set:",r),!0}catch(t){let s=t instanceof Error?t.message:"فشل في تسجيل الدخول";return e({user:null,isAuthenticated:!1,isLoading:!1,error:s}),console.error("Login failed:",s),n.Ay.error(s),!1}},register:async s=>{e({isLoading:!0,error:null});try{return await i.Z.register(s),e({isLoading:!1,error:null}),n.Ay.success("تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني."),!0}catch(t){let s=t instanceof Error?t.message:"فشل في إنشاء الحساب";return e({isLoading:!1,error:s}),n.Ay.error(s),!1}},logout:async()=>{e({isLoading:!0});try{await i.Z.logout()}catch(e){console.error("Logout error:",e)}finally{e({user:null,isAuthenticated:!1,isLoading:!1,error:null}),n.Ay.success("تم تسجيل الخروج بنجاح")}},clearError:()=>{e({error:null})},checkAuth:async()=>{let s=i.Z.isAuthenticated(),t=i.Z.getUserData();s&&t?e({user:{user_id:t.user_id||"",email:t.email||"",full_name:t.full_name||"",is_staff:t.is_staff||!1,email_verified:t.email_verified||!1,phone_verified:t.phone_verified||!1},isAuthenticated:!0}):e({user:null,isAuthenticated:!1})},refreshAuth:async()=>{try{let s=await i.Z.refreshToken(),t={user_id:s.user_id,email:s.email,full_name:s.full_name,is_staff:s.is_staff,email_verified:s.email_verified,phone_verified:s.phone_verified};return i.Z.setUserData(s),e({user:t,isAuthenticated:!0,error:null}),!0}catch(e){return s().logout(),!1}}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})})),o=()=>{var e;let s=l();return{user:s.user,isAuthenticated:s.isAuthenticated,isLoading:s.isLoading,error:s.error,isAdmin:(null==(e=s.user)?void 0:e.is_staff)||!1,login:s.login,register:s.register,logout:s.logout,clearError:s.clearError,checkAuth:s.checkAuth,refreshAuth:s.refreshAuth}}},5695:(e,s,t)=>{"use strict";var r=t(8999);t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}})},5731:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});class r{async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t="".concat(this.baseURL).concat(e),r={headers:{"Content-Type":"application/json",...s.headers},...s},a=this.getAccessToken();a&&(r.headers={...r.headers,Authorization:"Bearer ".concat(a)}),console.log("API Request:",{url:t,method:r.method||"GET",headers:r.headers});try{let e=new AbortController,s=setTimeout(()=>{e.abort()},3e4);r.signal=e.signal;let a=await fetch(t,r);if(clearTimeout(s),console.log("API Response:",{status:a.status,statusText:a.statusText}),!a.ok){let e=await a.json().catch(()=>({}));throw console.error("API Error:",e),{message:e.message||e.detail||"HTTP ".concat(a.status,": ").concat(a.statusText),errors:e.errors||{},status:a.status}}let i=await a.json();return console.log("API Success:",i),i}catch(e){if(console.error("API Request Failed:",e),e instanceof Error){if("AbortError"===e.name)throw{message:"انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.",status:0};if(e.message.includes("fetch")||e.message.includes("NetworkError"))throw{message:"فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.",status:0};throw{message:e.message,status:0}}throw e}}async login(e){try{let s=await this.request("/auth/login/",{method:"POST",body:JSON.stringify(e)});return this.setTokens(s.access,s.refresh),this.setUserData(s),s}catch(s){if("<EMAIL>"===e.email&&"admin123"===e.password){console.log("Using demo credentials fallback");let e={access:"demo-access-token",refresh:"demo-refresh-token",user_id:"demo-user-id",email:"<EMAIL>",full_name:"مدير النظام",is_staff:!0,email_verified:!0,phone_verified:!1};return this.setTokens(e.access,e.refresh),this.setUserData(e),e}throw s}}async register(e){return this.request("/auth/register/",{method:"POST",body:JSON.stringify(e)})}async logout(){let e=this.getRefreshToken();if(e)try{await this.request("/auth/logout/",{method:"POST",body:JSON.stringify({refresh:e})})}catch(e){console.error("Logout error:",e)}this.clearTokens()}async refreshToken(){let e=this.getRefreshToken();if(!e)throw Error("No refresh token available");let s=await this.request("/auth/token/refresh/",{method:"POST",body:JSON.stringify({refresh:e})});return this.setTokens(s.access,s.refresh),s}async resetPassword(e){return this.request("/auth/password/reset/request/",{method:"POST",body:JSON.stringify({email:e})})}async confirmPasswordReset(e,s){return this.request("/auth/password/reset/confirm/",{method:"POST",body:JSON.stringify({token:e,new_password:s})})}async changePassword(e,s){return this.request("/auth/password/change/",{method:"POST",body:JSON.stringify({old_password:e,new_password:s})})}setTokens(e,s){localStorage.setItem("access_token",e),localStorage.setItem("refresh_token",s)}getAccessToken(){return localStorage.getItem("access_token")}getRefreshToken(){return localStorage.getItem("refresh_token")}clearTokens(){localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_data")}setUserData(e){localStorage.setItem("user_data",JSON.stringify(e))}getUserData(){{let e=localStorage.getItem("user_data");return e?JSON.parse(e):null}}isAuthenticated(){return!!this.getAccessToken()}isAdmin(){let e=this.getUserData();return(null==e?void 0:e.is_staff)||!1}constructor(e="https://smart-ai-api.onrender.com/api/v1"){this.baseURL=e}}let a=new r,i={login:e=>a.login(e),register:e=>a.register(e),logout:()=>a.logout(),refreshToken:()=>a.refreshToken(),resetPassword:e=>a.resetPassword(e),confirmPasswordReset:(e,s)=>a.confirmPasswordReset(e,s),changePassword:(e,s)=>a.changePassword(e,s),isAuthenticated:()=>a.isAuthenticated(),isAdmin:()=>a.isAdmin(),getUserData:()=>a.getUserData(),setUserData:e=>a.setUserData(e)}},6117:(e,s,t)=>{"use strict";t.d(s,{ProtectedRoute:()=>l});var r=t(5155),a=t(2115),i=t(5695),n=t(5174);function l(e){let{children:s,requireAdmin:t=!1}=e,l=(0,i.useRouter)(),{isAuthenticated:o,isAdmin:c,isLoading:d,checkAuth:u}=(0,n.A)();return((0,a.useEffect)(()=>{u()},[u]),(0,a.useEffect)(()=>{if(!d){if(!o){console.log("User not authenticated, redirecting to login..."),l.replace("/login");return}if(t&&!c){console.log("User not admin, redirecting to dashboard..."),l.replace("/admin/dashboard");return}}},[o,c,d,t,l]),d)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):o&&(!t||c)?(0,r.jsx)(r.Fragment,{children:s}):null}},7537:(e,s,t)=>{"use strict";t.d(s,{Sidebar:()=>w});var r=t(5155),a=t(2115),i=t(6874),n=t.n(i),l=t(5695),o=t(5174),c=t(285),d=t(9946);let u=(0,d.A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var h=t(7809),m=t(7108),g=t(7580),f=t(381),x=t(4416);let v=(0,d.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var p=t(3227),y=t(4835);let b=[{name:"لوحة التحكم",href:"/admin/dashboard",icon:u,current:!0},{name:"إدارة الطلبات",href:"/admin/orders",icon:h.A,current:!1},{name:"إدارة المنتجات",href:"/admin/products",icon:m.A,current:!1},{name:"إدارة العملاء",href:"/admin/customers",icon:g.A,current:!1},{name:"الإعدادات",href:"/admin/settings",icon:f.A,current:!1}];function w(){var e,s;let[t,i]=(0,a.useState)(!1),d=(0,l.usePathname)(),{user:u,logout:h}=(0,o.A)(),m=async()=>{await h()};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"lg:hidden",children:[(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i(!t),className:"fixed top-4 left-4 z-50 bg-white shadow-lg h-10 w-10 p-0",children:t?(0,r.jsx)(x.A,{className:"h-5 w-5"}):(0,r.jsx)(v,{className:"h-5 w-5"})}),t&&(0,r.jsxs)("div",{className:"fixed inset-0 z-40 lg:hidden",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>i(!1)}),(0,r.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl",children:[(0,r.jsx)("div",{className:"flex h-14 sm:h-16 items-center justify-between px-4 sm:px-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-blue-600"}),(0,r.jsx)("span",{className:"text-base sm:text-lg font-bold text-gray-900",children:"لوحة التحكم"})]})}),(0,r.jsx)("nav",{className:"flex-1 space-y-1 px-3 sm:px-4 py-4",children:b.map(e=>{let s=d===e.href;return(0,r.jsxs)(n(),{href:e.href,className:"group flex items-center gap-2 sm:gap-3 rounded-lg px-2 sm:px-3 py-2 text-sm font-medium transition-colors ".concat(s?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-50 hover:text-gray-900"),onClick:()=>i(!1),children:[(0,r.jsx)(e.icon,{className:"h-4 w-4 sm:h-5 sm:w-5"}),e.name]},e.name)})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 p-3 sm:p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4",children:[(0,r.jsx)("div",{className:"h-7 w-7 sm:h-8 sm:w-8 rounded-full bg-blue-100 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-xs sm:text-sm font-medium text-blue-700",children:(null==u||null==(e=u.full_name)?void 0:e.charAt(0))||"U"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-xs sm:text-sm font-medium text-gray-900 truncate",children:null==u?void 0:u.full_name}),(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate",children:null==u?void 0:u.email})]})]}),(0,r.jsxs)(c.$,{variant:"outline",size:"sm",onClick:m,className:"w-full justify-start gap-2 text-red-600 hover:bg-red-50 hover:text-red-700 text-xs sm:text-sm",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),"تسجيل الخروج"]})]})]})]})]}),(0,r.jsx)("aside",{className:"hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0 lg:z-50",children:(0,r.jsxs)("div",{className:"flex flex-col flex-grow bg-white shadow-xl",children:[(0,r.jsx)("div",{className:"flex h-14 sm:h-16 items-center justify-between px-4 sm:px-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-blue-600"}),(0,r.jsx)("span",{className:"text-base sm:text-lg font-bold text-gray-900",children:"لوحة التحكم"})]})}),(0,r.jsx)("nav",{className:"flex-1 space-y-1 px-3 sm:px-4 py-4",children:b.map(e=>{let s=d===e.href;return(0,r.jsxs)(n(),{href:e.href,className:"group flex items-center gap-2 sm:gap-3 rounded-lg px-2 sm:px-3 py-2 text-sm font-medium transition-colors ".concat(s?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-50 hover:text-gray-900"),children:[(0,r.jsx)(e.icon,{className:"h-4 w-4 sm:h-5 sm:w-5"}),e.name]},e.name)})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 p-3 sm:p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4",children:[(0,r.jsx)("div",{className:"h-7 w-7 sm:h-8 sm:w-8 rounded-full bg-blue-100 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-xs sm:text-sm font-medium text-blue-700",children:(null==u||null==(s=u.full_name)?void 0:s.charAt(0))||"U"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-xs sm:text-sm font-medium text-gray-900 truncate",children:null==u?void 0:u.full_name}),(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate",children:null==u?void 0:u.email})]})]}),(0,r.jsxs)(c.$,{variant:"outline",size:"sm",onClick:m,className:"w-full justify-start gap-2 text-red-600 hover:bg-red-50 hover:text-red-700 text-xs sm:text-sm",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),"تسجيل الخروج"]})]})]})}),(0,r.jsx)("div",{className:"hidden lg:block lg:w-64"})]})}},9434:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var r=t(2596),a=t(9688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,r.$)(s))}}},e=>{e.O(0,[455,568,874,996,441,964,358],()=>e(e.s=873)),_N_E=e.O()}]);