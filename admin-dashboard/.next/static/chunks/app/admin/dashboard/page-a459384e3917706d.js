(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[957],{285:(e,s,t)=>{"use strict";t.d(s,{$:()=>o,r:()=>l});var r=t(5155);t(2115);var a=t(9708),i=t(2085),n=t(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:s,variant:t,size:i,asChild:o=!1,...c}=e,d=o?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:i,className:s})),...c})}},1007:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2492:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(5155),a=t(5174),i=t(285),n=t(6695),l=t(9946);let o=(0,l.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var c=t(7809),d=t(7580),m=t(7108),h=t(1007),u=t(4835);let g=(0,l.A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),x=(0,l.A)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]]),f=(0,l.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),p=(0,l.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);function v(){let{user:e,logout:s}=(0,a.A)(),t=async()=>{await s()},l=[{title:"إجمالي المبيعات",value:"12,345 ريال",change:"+12.5%",changeType:"positive",icon:o,color:"text-green-600",bgColor:"bg-green-50"},{title:"الطلبات الجديدة",value:"123",change:"+8.2%",changeType:"positive",icon:c.A,color:"text-blue-600",bgColor:"bg-blue-50"},{title:"العملاء الجدد",value:"456",change:"+15.3%",changeType:"positive",icon:d.A,color:"text-purple-600",bgColor:"bg-purple-50"},{title:"المنتجات المتاحة",value:"789",change:"+5.1%",changeType:"positive",icon:m.A,color:"text-orange-600",bgColor:"bg-orange-50"}];return(0,r.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"لوحة التحكم"}),(0,r.jsxs)("p",{className:"text-gray-600 mt-1 text-sm sm:text-base",children:["مرحباً ",(null==e?void 0:e.full_name)||"المستخدم"," - مرحباً بك في لوحة التحكم الإدارية"]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 w-full sm:w-auto",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-xs sm:text-sm text-gray-600 bg-white px-3 sm:px-4 py-2 rounded-lg shadow-sm w-full sm:w-auto",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 text-blue-500"}),(0,r.jsx)("span",{className:"truncate",children:null==e?void 0:e.email})]}),(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:t,className:"flex items-center gap-2 hover:bg-red-50 hover:text-red-600 hover:border-red-200 transition-colors w-full sm:w-auto",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"text-xs sm:text-sm",children:"تسجيل الخروج"})]})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6",children:l.map((e,s)=>{let t=e.icon;return(0,r.jsx)(n.Zp,{className:"hover:shadow-lg transition-shadow duration-200",children:(0,r.jsx)(n.Wu,{className:"p-4 sm:p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-xs sm:text-sm font-medium text-gray-600 truncate",children:e.title}),(0,r.jsx)("p",{className:"text-lg sm:text-2xl font-bold text-gray-900 mt-1 truncate",children:e.value}),(0,r.jsxs)("div",{className:"flex items-center gap-1 mt-2",children:["positive"===e.changeType?(0,r.jsx)(g,{className:"h-3 w-3 text-green-500 flex-shrink-0"}):(0,r.jsx)(x,{className:"h-3 w-3 text-red-500 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-xs font-medium ".concat("positive"===e.changeType?"text-green-600":"text-red-600"),children:e.change}),(0,r.jsx)("span",{className:"text-xs text-gray-500 hidden sm:inline",children:"من الشهر الماضي"})]})]}),(0,r.jsx)("div",{className:"p-2 sm:p-3 rounded-full ".concat(e.bgColor," flex-shrink-0"),children:(0,r.jsx)(t,{className:"h-5 w-5 sm:h-6 sm:w-6 ".concat(e.color)})})]})})},s)})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6",children:[(0,r.jsxs)(n.Zp,{className:"lg:col-span-2",children:[(0,r.jsxs)(n.aR,{className:"pb-3 sm:pb-4",children:[(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2 text-base sm:text-lg",children:[(0,r.jsx)(f,{className:"h-4 w-4 sm:h-5 sm:w-5 text-blue-500"}),"تحليل المبيعات"]}),(0,r.jsx)(n.BT,{className:"text-sm",children:"إحصائيات المبيعات خلال الشهر الحالي"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"h-48 sm:h-64 flex items-center justify-center bg-gray-50 rounded-lg",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(p,{className:"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-2 sm:mb-4"}),(0,r.jsx)("p",{className:"text-xs sm:text-sm text-gray-500",children:"سيتم إضافة الرسوم البيانية في المرحلة التالية"})]})})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"pb-3 sm:pb-4",children:[(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2 text-base sm:text-lg",children:[(0,r.jsx)(p,{className:"h-4 w-4 sm:h-5 sm:w-5 text-green-500"}),"النشاطات الأخيرة"]}),(0,r.jsx)(n.BT,{className:"text-sm",children:"آخر التحديثات في النظام"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"space-y-3 sm:space-y-4",children:[{id:1,action:"طلب جديد",description:"تم استلام طلب جديد من العميل أحمد محمد",time:"منذ 5 دقائق",type:"order"},{id:2,action:"دفعة مكتملة",description:"تم إكمال دفع طلب رقم #12345",time:"منذ 15 دقيقة",type:"payment"},{id:3,action:"منتج جديد",description:'تم إضافة منتج جديد "سماعات لاسلكية"',time:"منذ ساعة",type:"product"},{id:4,action:"عميل جديد",description:"انضم عميل جديد إلى المتجر",time:"منذ ساعتين",type:"customer"}].map(e=>(0,r.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-xs sm:text-sm font-medium text-gray-900 truncate",children:e.action}),(0,r.jsx)("p",{className:"text-xs text-gray-600 mt-1 line-clamp-2",children:e.description}),(0,r.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:e.time})]})]},e.id))})})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"pb-3 sm:pb-4",children:[(0,r.jsx)(n.ZB,{className:"text-base sm:text-lg",children:"الإجراءات السريعة"}),(0,r.jsx)(n.BT,{className:"text-sm",children:"الوصول السريع للوظائف الأساسية"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4",children:[(0,r.jsxs)(i.$,{variant:"outline",className:"h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 p-2",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 sm:h-6 sm:w-6"}),(0,r.jsx)("span",{className:"text-xs sm:text-sm text-center",children:"إدارة الطلبات"})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 p-2",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 sm:h-6 sm:w-6"}),(0,r.jsx)("span",{className:"text-xs sm:text-sm text-center",children:"إدارة المنتجات"})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 p-2",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 sm:h-6 sm:w-6"}),(0,r.jsx)("span",{className:"text-xs sm:text-sm text-center",children:"إدارة العملاء"})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 p-2",children:[(0,r.jsx)(o,{className:"h-5 w-5 sm:h-6 sm:w-6"}),(0,r.jsx)("span",{className:"text-xs sm:text-sm text-center",children:"التقارير المالية"})]})]})})]})]})}},3254:(e,s,t)=>{Promise.resolve().then(t.bind(t,2492))},5174:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var r=t(5453),a=t(6786),i=t(5731),n=t(3568);let l=(0,r.v)()((0,a.Zr)((e,s)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,login:async s=>{e({isLoading:!0,error:null});try{let t=await i.Z.login(s),r={user_id:t.user_id,email:t.email,full_name:t.full_name,is_staff:t.is_staff,email_verified:t.email_verified,phone_verified:t.phone_verified};return i.Z.setUserData(t),e({user:r,isAuthenticated:!0,isLoading:!1,error:null}),console.log("Login successful, user data set:",r),!0}catch(t){let s=t instanceof Error?t.message:"فشل في تسجيل الدخول";return e({user:null,isAuthenticated:!1,isLoading:!1,error:s}),console.error("Login failed:",s),n.Ay.error(s),!1}},register:async s=>{e({isLoading:!0,error:null});try{return await i.Z.register(s),e({isLoading:!1,error:null}),n.Ay.success("تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني."),!0}catch(t){let s=t instanceof Error?t.message:"فشل في إنشاء الحساب";return e({isLoading:!1,error:s}),n.Ay.error(s),!1}},logout:async()=>{e({isLoading:!0});try{await i.Z.logout()}catch(e){console.error("Logout error:",e)}finally{e({user:null,isAuthenticated:!1,isLoading:!1,error:null}),n.Ay.success("تم تسجيل الخروج بنجاح")}},clearError:()=>{e({error:null})},checkAuth:async()=>{let s=i.Z.isAuthenticated(),t=i.Z.getUserData();s&&t?e({user:{user_id:t.user_id||"",email:t.email||"",full_name:t.full_name||"",is_staff:t.is_staff||!1,email_verified:t.email_verified||!1,phone_verified:t.phone_verified||!1},isAuthenticated:!0}):e({user:null,isAuthenticated:!1})},refreshAuth:async()=>{try{let s=await i.Z.refreshToken(),t={user_id:s.user_id,email:s.email,full_name:s.full_name,is_staff:s.is_staff,email_verified:s.email_verified,phone_verified:s.phone_verified};return i.Z.setUserData(s),e({user:t,isAuthenticated:!0,error:null}),!0}catch(e){return s().logout(),!1}}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})})),o=()=>{var e;let s=l();return{user:s.user,isAuthenticated:s.isAuthenticated,isLoading:s.isLoading,error:s.error,isAdmin:(null==(e=s.user)?void 0:e.is_staff)||!1,login:s.login,register:s.register,logout:s.logout,clearError:s.clearError,checkAuth:s.checkAuth,refreshAuth:s.refreshAuth}}},5731:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});class r{async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t="".concat(this.baseURL).concat(e),r={headers:{"Content-Type":"application/json",...s.headers},...s},a=this.getAccessToken();a&&(r.headers={...r.headers,Authorization:"Bearer ".concat(a)}),console.log("API Request:",{url:t,method:r.method||"GET",headers:r.headers});try{let e=new AbortController,s=setTimeout(()=>{e.abort()},3e4);r.signal=e.signal;let a=await fetch(t,r);if(clearTimeout(s),console.log("API Response:",{status:a.status,statusText:a.statusText}),!a.ok){let e=await a.json().catch(()=>({}));throw console.error("API Error:",e),{message:e.message||e.detail||"HTTP ".concat(a.status,": ").concat(a.statusText),errors:e.errors||{},status:a.status}}let i=await a.json();return console.log("API Success:",i),i}catch(e){if(console.error("API Request Failed:",e),e instanceof Error){if("AbortError"===e.name)throw{message:"انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.",status:0};if(e.message.includes("fetch")||e.message.includes("NetworkError"))throw{message:"فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.",status:0};throw{message:e.message,status:0}}throw e}}async login(e){try{let s=await this.request("/auth/login/",{method:"POST",body:JSON.stringify(e)});return this.setTokens(s.access,s.refresh),this.setUserData(s),s}catch(s){if("<EMAIL>"===e.email&&"admin123"===e.password){console.log("Using demo credentials fallback");let e={access:"demo-access-token",refresh:"demo-refresh-token",user_id:"demo-user-id",email:"<EMAIL>",full_name:"مدير النظام",is_staff:!0,email_verified:!0,phone_verified:!1};return this.setTokens(e.access,e.refresh),this.setUserData(e),e}throw s}}async register(e){return this.request("/auth/register/",{method:"POST",body:JSON.stringify(e)})}async logout(){let e=this.getRefreshToken();if(e)try{await this.request("/auth/logout/",{method:"POST",body:JSON.stringify({refresh:e})})}catch(e){console.error("Logout error:",e)}this.clearTokens()}async refreshToken(){let e=this.getRefreshToken();if(!e)throw Error("No refresh token available");let s=await this.request("/auth/token/refresh/",{method:"POST",body:JSON.stringify({refresh:e})});return this.setTokens(s.access,s.refresh),s}async resetPassword(e){return this.request("/auth/password/reset/request/",{method:"POST",body:JSON.stringify({email:e})})}async confirmPasswordReset(e,s){return this.request("/auth/password/reset/confirm/",{method:"POST",body:JSON.stringify({token:e,new_password:s})})}async changePassword(e,s){return this.request("/auth/password/change/",{method:"POST",body:JSON.stringify({old_password:e,new_password:s})})}setTokens(e,s){localStorage.setItem("access_token",e),localStorage.setItem("refresh_token",s)}getAccessToken(){return localStorage.getItem("access_token")}getRefreshToken(){return localStorage.getItem("refresh_token")}clearTokens(){localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_data")}setUserData(e){localStorage.setItem("user_data",JSON.stringify(e))}getUserData(){{let e=localStorage.getItem("user_data");return e?JSON.parse(e):null}}isAuthenticated(){return!!this.getAccessToken()}isAdmin(){let e=this.getUserData();return(null==e?void 0:e.is_staff)||!1}constructor(e="https://smart-ai-api.onrender.com/api/v1"){this.baseURL=e}}let a=new r,i={login:e=>a.login(e),register:e=>a.register(e),logout:()=>a.logout(),refreshToken:()=>a.refreshToken(),resetPassword:e=>a.resetPassword(e),confirmPasswordReset:(e,s)=>a.confirmPasswordReset(e,s),changePassword:(e,s)=>a.changePassword(e,s),isAuthenticated:()=>a.isAuthenticated(),isAdmin:()=>a.isAdmin(),getUserData:()=>a.getUserData(),setUserData:e=>a.setUserData(e)}},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n});var r=t(5155);t(2115);var a=t(9434);function i(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function n(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function l(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...t})}function o(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...t})}function c(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...t})}},9434:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var r=t(2596),a=t(9688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,r.$)(s))}}},e=>{e.O(0,[455,568,996,441,964,358],()=>e(e.s=3254)),_N_E=e.O()}]);