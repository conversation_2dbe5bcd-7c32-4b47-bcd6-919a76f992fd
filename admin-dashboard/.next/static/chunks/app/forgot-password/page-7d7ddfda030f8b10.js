(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[162],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o,r:()=>l});var r=s(5155);s(2115);var a=s(9708),n=s(2085),i=s(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:n,asChild:o=!1,...c}=e,d=o?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(l({variant:s,size:n,className:t})),...c})}},646:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1277:(e,t,s)=>{Promise.resolve().then(s.bind(s,4070))},2523:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(5155);s(2115);var a=s(9434);function n(e){let{className:t,type:s,...n}=e;return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},4070:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(5155),a=s(2115),n=s(2177),i=s(221),l=s(8309),o=s(6874),c=s.n(o),d=s(646);let u=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var m=s(8883),h=s(285),x=s(2523),g=s(6695),f=s(7759),p=s(5731),v=s(3568);let b=l.Ik({email:l.Yj().min(1,"البريد الإلكتروني مطلوب").email("يرجى إدخال بريد إلكتروني صحيح")});function y(){let[e,t]=(0,a.useState)(!1),[s,l]=(0,a.useState)(!1),[o,y]=(0,a.useState)(""),j=(0,n.mN)({resolver:(0,i.u)(b),defaultValues:{email:""}}),N=async e=>{t(!0);try{await p.Z.resetPassword(e.email),y(e.email),l(!0),v.Ay.success("تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني")}catch(t){let e=t instanceof Error?t.message:"حدث خطأ أثناء إرسال رابط إعادة التعيين";v.Ay.error(e)}finally{t(!1)}};return s?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100",children:(0,r.jsx)(d.A,{className:"h-6 w-6 text-green-600"})}),(0,r.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"تم الإرسال بنجاح"}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"تحقق من بريدك الإلكتروني"})]}),(0,r.jsxs)(g.Zp,{children:[(0,r.jsxs)(g.aR,{className:"space-y-1",children:[(0,r.jsx)(g.ZB,{className:"text-2xl text-center",children:"تحقق من بريدك الإلكتروني"}),(0,r.jsx)(g.BT,{className:"text-center",children:"لقد أرسلنا رابط إعادة تعيين كلمة المرور إلى"})]}),(0,r.jsxs)(g.Wu,{className:"space-y-4",children:[(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("p",{className:"text-sm font-medium text-blue-600 bg-blue-50 p-3 rounded-md",children:o})}),(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"يرجى التحقق من صندوق الوارد الخاص بك واتباع التعليمات لإعادة تعيين كلمة المرور."}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"لم تتلق الرسالة؟ تحقق من مجلد الرسائل غير المرغوب فيها أو انتظر بضع دقائق."}),(0,r.jsx)("div",{className:"pt-4",children:(0,r.jsx)(c(),{href:"/login",children:(0,r.jsxs)(h.$,{variant:"outline",className:"w-full",children:[(0,r.jsx)(u,{className:"h-4 w-4 mr-2"}),"العودة إلى تسجيل الدخول"]})})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("button",{onClick:()=>{l(!1),j.reset()},className:"text-sm text-blue-600 hover:text-blue-500",children:"إرسال إلى بريد إلكتروني آخر"})})]})]})]})]})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100",children:(0,r.jsx)(m.A,{className:"h-6 w-6 text-blue-600"})}),(0,r.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"نسيت كلمة المرور؟"}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور"})]}),(0,r.jsxs)(g.Zp,{children:[(0,r.jsxs)(g.aR,{className:"space-y-1",children:[(0,r.jsx)(g.ZB,{className:"text-2xl text-center",children:"إعادة تعيين كلمة المرور"}),(0,r.jsx)(g.BT,{className:"text-center",children:"سنرسل لك رابط إعادة تعيين كلمة المرور"})]}),(0,r.jsx)(g.Wu,{children:(0,r.jsx)(f.lV,{...j,children:(0,r.jsxs)("form",{onSubmit:j.handleSubmit(N),className:"space-y-4",children:[(0,r.jsx)(f.zB,{control:j.control,name:"email",render:t=>{let{field:s}=t;return(0,r.jsxs)(f.eI,{children:[(0,r.jsxs)(f.lR,{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),"البريد الإلكتروني"]}),(0,r.jsx)(f.MJ,{children:(0,r.jsx)(x.p,{...s,type:"email",placeholder:"<EMAIL>",disabled:e,className:"text-right",dir:"ltr"})}),(0,r.jsx)(f.C5,{})]})}}),(0,r.jsx)(h.$,{type:"submit",className:"w-full",disabled:e,children:e?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"}),"جاري الإرسال..."]}):(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),"إرسال رابط إعادة التعيين"]})}),(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsxs)(c(),{href:"/login",className:"text-sm text-blue-600 hover:text-blue-500 flex items-center justify-center gap-2",children:[(0,r.jsx)(u,{className:"h-4 w-4"}),"العودة إلى تسجيل الدخول"]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["ليس لديك حساب؟"," ",(0,r.jsx)(c(),{href:"/register",className:"text-blue-600 hover:text-blue-500 font-medium",children:"إنشاء حساب جديد"})]})]})]})})})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"\xa9 2025 لوحة التحكم الإدارية. جميع الحقوق محفوظة."})})]})})}},5057:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var r=s(5155);s(2115);var a=s(968),n=s(9434);function i(e){let{className:t,...s}=e;return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},5731:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});class r{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s="".concat(this.baseURL).concat(e),r={headers:{"Content-Type":"application/json",...t.headers},...t},a=this.getAccessToken();a&&(r.headers={...r.headers,Authorization:"Bearer ".concat(a)}),console.log("API Request:",{url:s,method:r.method||"GET",headers:r.headers});try{let e=new AbortController,t=setTimeout(()=>{e.abort()},3e4);r.signal=e.signal;let a=await fetch(s,r);if(clearTimeout(t),console.log("API Response:",{status:a.status,statusText:a.statusText}),!a.ok){let e=await a.json().catch(()=>({}));throw console.error("API Error:",e),{message:e.message||e.detail||"HTTP ".concat(a.status,": ").concat(a.statusText),errors:e.errors||{},status:a.status}}let n=await a.json();return console.log("API Success:",n),n}catch(e){if(console.error("API Request Failed:",e),e instanceof Error){if("AbortError"===e.name)throw{message:"انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.",status:0};if(e.message.includes("fetch")||e.message.includes("NetworkError"))throw{message:"فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.",status:0};throw{message:e.message,status:0}}throw e}}async login(e){try{let t=await this.request("/auth/login/",{method:"POST",body:JSON.stringify(e)});return this.setTokens(t.access,t.refresh),this.setUserData(t),t}catch(t){if("<EMAIL>"===e.email&&"admin123"===e.password){console.log("Using demo credentials fallback");let e={access:"demo-access-token",refresh:"demo-refresh-token",user_id:"demo-user-id",email:"<EMAIL>",full_name:"مدير النظام",is_staff:!0,email_verified:!0,phone_verified:!1};return this.setTokens(e.access,e.refresh),this.setUserData(e),e}throw t}}async register(e){return this.request("/auth/register/",{method:"POST",body:JSON.stringify(e)})}async logout(){let e=this.getRefreshToken();if(e)try{await this.request("/auth/logout/",{method:"POST",body:JSON.stringify({refresh:e})})}catch(e){console.error("Logout error:",e)}this.clearTokens()}async refreshToken(){let e=this.getRefreshToken();if(!e)throw Error("No refresh token available");let t=await this.request("/auth/token/refresh/",{method:"POST",body:JSON.stringify({refresh:e})});return this.setTokens(t.access,t.refresh),t}async resetPassword(e){return this.request("/auth/password/reset/request/",{method:"POST",body:JSON.stringify({email:e})})}async confirmPasswordReset(e,t){return this.request("/auth/password/reset/confirm/",{method:"POST",body:JSON.stringify({token:e,new_password:t})})}async changePassword(e,t){return this.request("/auth/password/change/",{method:"POST",body:JSON.stringify({old_password:e,new_password:t})})}setTokens(e,t){localStorage.setItem("access_token",e),localStorage.setItem("refresh_token",t)}getAccessToken(){return localStorage.getItem("access_token")}getRefreshToken(){return localStorage.getItem("refresh_token")}clearTokens(){localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_data")}setUserData(e){localStorage.setItem("user_data",JSON.stringify(e))}getUserData(){{let e=localStorage.getItem("user_data");return e?JSON.parse(e):null}}isAuthenticated(){return!!this.getAccessToken()}isAdmin(){let e=this.getUserData();return(null==e?void 0:e.is_staff)||!1}constructor(e="https://smart-ai-api.onrender.com/api/v1"){this.baseURL=e}}let a=new r,n={login:e=>a.login(e),register:e=>a.register(e),logout:()=>a.logout(),refreshToken:()=>a.refreshToken(),resetPassword:e=>a.resetPassword(e),confirmPasswordReset:(e,t)=>a.confirmPasswordReset(e,t),changePassword:(e,t)=>a.changePassword(e,t),isAuthenticated:()=>a.isAuthenticated(),isAdmin:()=>a.isAdmin(),getUserData:()=>a.getUserData(),setUserData:e=>a.setUserData(e)}},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i});var r=s(5155);s(2115);var a=s(9434);function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function i(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...s})}function o(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...s})}function c(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...s})}},7759:(e,t,s)=>{"use strict";s.d(t,{C5:()=>p,MJ:()=>f,eI:()=>x,lR:()=>g,lV:()=>c,zB:()=>u});var r=s(5155),a=s(2115),n=s(9708),i=s(2177),l=s(9434),o=s(5057);let c=i.Op,d=a.createContext({}),u=e=>{let{...t}=e;return(0,r.jsx)(d.Provider,{value:{name:t.name},children:(0,r.jsx)(i.xI,{...t})})},m=()=>{let e=a.useContext(d),t=a.useContext(h),{getFieldState:s}=(0,i.xW)(),r=(0,i.lN)({name:e.name}),n=s(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...n}},h=a.createContext({});function x(e){let{className:t,...s}=e,n=a.useId();return(0,r.jsx)(h.Provider,{value:{id:n},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",t),...s})})}function g(e){let{className:t,...s}=e,{error:a,formItemId:n}=m();return(0,r.jsx)(o.J,{"data-slot":"form-label","data-error":!!a,className:(0,l.cn)("data-[error=true]:text-destructive",t),htmlFor:n,...s})}function f(e){let{...t}=e,{error:s,formItemId:a,formDescriptionId:i,formMessageId:l}=m();return(0,r.jsx)(n.DX,{"data-slot":"form-control",id:a,"aria-describedby":s?"".concat(i," ").concat(l):"".concat(i),"aria-invalid":!!s,...t})}function p(e){var t;let{className:s,...a}=e,{error:n,formMessageId:i}=m(),o=n?String(null!=(t=null==n?void 0:n.message)?t:""):a.children;return o?(0,r.jsx)("p",{"data-slot":"form-message",id:i,className:(0,l.cn)("text-destructive text-sm",s),...a,children:o}):null}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(2596),a=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}}},e=>{e.O(0,[455,568,874,72,299,441,964,358],()=>e(e.s=1277)),_N_E=e.O()}]);