(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{285:(e,s,r)=>{"use strict";r.d(s,{$:()=>l,r:()=>o});var t=r(5155);r(2115);var a=r(9708),i=r(2085),n=r(9434);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:s,variant:r,size:i,asChild:l=!1,...c}=e,d=l?a.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:i,className:s})),...c})}},3792:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var t=r(5155),a=r(2115),i=r(5695),n=r(5174),o=r(285),l=r(6695),c=r(5525),d=r(7580),u=r(2713),h=r(381);function m(){let e=(0,i.useRouter)(),{isAuthenticated:s,isLoading:r,checkAuth:m}=(0,n.A)();return((0,a.useEffect)(()=>{m()},[m]),(0,a.useEffect)(()=>{!r&&s&&e.push("/admin/welcome")},[s,r,e]),r)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[(0,t.jsxs)("div",{className:"text-center mb-16",children:[(0,t.jsx)("div",{className:"mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-blue-100 mb-6",children:(0,t.jsx)(c.A,{className:"h-8 w-8 text-blue-600"})}),(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"لوحة التحكم الإدارية"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"نظام إدارة متقدم للمتجر الإلكتروني مع واجهة حديثة وسهلة الاستخدام"}),(0,t.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 border-2 border-green-200 rounded-xl p-6 max-w-2xl mx-auto mb-8",children:[(0,t.jsx)("h3",{className:"text-lg font-bold text-green-800 mb-3",children:"\uD83C\uDFAF جرب النظام الآن"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)("span",{className:"font-medium text-green-700",children:"البريد الإلكتروني:"}),(0,t.jsx)("code",{className:"bg-green-100 px-3 py-1 rounded text-green-800 font-mono",children:"<EMAIL>"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)("span",{className:"font-medium text-green-700",children:"كلمة المرور:"}),(0,t.jsx)("code",{className:"bg-green-100 px-3 py-1 rounded text-green-800 font-mono",children:"admin123"})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,t.jsx)(o.$,{onClick:()=>e.push("/login"),size:"lg",className:"px-8 bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800",children:"تسجيل الدخول"}),(0,t.jsx)(o.$,{onClick:()=>e.push("/register"),variant:"outline",size:"lg",className:"px-8 border-2 border-blue-600 text-blue-600 hover:bg-blue-50",children:"إنشاء حساب"})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.aR,{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100 mb-4",children:(0,t.jsx)(d.A,{className:"h-6 w-6 text-green-600"})}),(0,t.jsx)(l.ZB,{children:"إدارة المستخدمين"}),(0,t.jsx)(l.BT,{children:"إدارة شاملة لحسابات المستخدمين والصلاحيات"})]})}),(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.aR,{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-purple-100 mb-4",children:(0,t.jsx)(u.A,{className:"h-6 w-6 text-purple-600"})}),(0,t.jsx)(l.ZB,{children:"التقارير والإحصائيات"}),(0,t.jsx)(l.BT,{children:"تقارير مفصلة وإحصائيات شاملة للأداء"})]})}),(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.aR,{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-orange-100 mb-4",children:(0,t.jsx)(h.A,{className:"h-6 w-6 text-orange-600"})}),(0,t.jsx)(l.ZB,{children:"إعدادات النظام"}),(0,t.jsx)(l.BT,{children:"تخصيص وإعداد جميع جوانب النظام"})]})})]}),(0,t.jsx)("div",{className:"text-center mt-16",children:(0,t.jsxs)(l.Zp,{className:"max-w-2xl mx-auto",children:[(0,t.jsx)(l.aR,{children:(0,t.jsx)(l.ZB,{children:"مميزات النظام"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm text-gray-600",children:[(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:"• واجهة مستخدم حديثة وسهلة الاستخدام"}),(0,t.jsx)("li",{children:"• نظام أمان متقدم مع تشفير البيانات"}),(0,t.jsx)("li",{children:"• تقارير وإحصائيات في الوقت الفعلي"})]})}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:"• دعم كامل للغة العربية"}),(0,t.jsx)("li",{children:"• تصميم متجاوب لجميع الأجهزة"}),(0,t.jsx)("li",{children:"• نظام إشعارات ذكي"})]})})]})})]})}),(0,t.jsx)("div",{className:"text-center mt-8",children:(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"\xa9 2025 لوحة التحكم الإدارية. جميع الحقوق محفوظة."})})]})})}},5174:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});var t=r(5453),a=r(6786),i=r(5731),n=r(3568);let o=(0,t.v)()((0,a.Zr)((e,s)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,login:async s=>{e({isLoading:!0,error:null});try{let r=await i.Z.login(s),t={user_id:r.user_id,email:r.email,full_name:r.full_name,is_staff:r.is_staff,email_verified:r.email_verified,phone_verified:r.phone_verified};return i.Z.setUserData(r),e({user:t,isAuthenticated:!0,isLoading:!1,error:null}),console.log("Login successful, user data set:",t),!0}catch(r){let s=r instanceof Error?r.message:"فشل في تسجيل الدخول";return e({user:null,isAuthenticated:!1,isLoading:!1,error:s}),console.error("Login failed:",s),n.Ay.error(s),!1}},register:async s=>{e({isLoading:!0,error:null});try{return await i.Z.register(s),e({isLoading:!1,error:null}),n.Ay.success("تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني."),!0}catch(r){let s=r instanceof Error?r.message:"فشل في إنشاء الحساب";return e({isLoading:!1,error:s}),n.Ay.error(s),!1}},logout:async()=>{e({isLoading:!0});try{await i.Z.logout()}catch(e){console.error("Logout error:",e)}finally{e({user:null,isAuthenticated:!1,isLoading:!1,error:null}),n.Ay.success("تم تسجيل الخروج بنجاح")}},clearError:()=>{e({error:null})},checkAuth:async()=>{let s=i.Z.isAuthenticated(),r=i.Z.getUserData();s&&r?e({user:{user_id:r.user_id||"",email:r.email||"",full_name:r.full_name||"",is_staff:r.is_staff||!1,email_verified:r.email_verified||!1,phone_verified:r.phone_verified||!1},isAuthenticated:!0}):e({user:null,isAuthenticated:!1})},refreshAuth:async()=>{try{let s=await i.Z.refreshToken(),r={user_id:s.user_id,email:s.email,full_name:s.full_name,is_staff:s.is_staff,email_verified:s.email_verified,phone_verified:s.phone_verified};return i.Z.setUserData(s),e({user:r,isAuthenticated:!0,error:null}),!0}catch(e){return s().logout(),!1}}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})})),l=()=>{var e;let s=o();return{user:s.user,isAuthenticated:s.isAuthenticated,isLoading:s.isLoading,error:s.error,isAdmin:(null==(e=s.user)?void 0:e.is_staff)||!1,login:s.login,register:s.register,logout:s.logout,clearError:s.clearError,checkAuth:s.checkAuth,refreshAuth:s.refreshAuth}}},5731:(e,s,r)=>{"use strict";r.d(s,{Z:()=>i});class t{async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat(this.baseURL).concat(e),t={headers:{"Content-Type":"application/json",...s.headers},...s},a=this.getAccessToken();a&&(t.headers={...t.headers,Authorization:"Bearer ".concat(a)}),console.log("API Request:",{url:r,method:t.method||"GET",headers:t.headers});try{let e=new AbortController,s=setTimeout(()=>{e.abort()},3e4);t.signal=e.signal;let a=await fetch(r,t);if(clearTimeout(s),console.log("API Response:",{status:a.status,statusText:a.statusText}),!a.ok){let e=await a.json().catch(()=>({}));throw console.error("API Error:",e),{message:e.message||e.detail||"HTTP ".concat(a.status,": ").concat(a.statusText),errors:e.errors||{},status:a.status}}let i=await a.json();return console.log("API Success:",i),i}catch(e){if(console.error("API Request Failed:",e),e instanceof Error){if("AbortError"===e.name)throw{message:"انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.",status:0};if(e.message.includes("fetch")||e.message.includes("NetworkError"))throw{message:"فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.",status:0};throw{message:e.message,status:0}}throw e}}async login(e){try{let s=await this.request("/auth/login/",{method:"POST",body:JSON.stringify(e)});return this.setTokens(s.access,s.refresh),this.setUserData(s),s}catch(s){if("<EMAIL>"===e.email&&"admin123"===e.password){console.log("Using demo credentials fallback");let e={access:"demo-access-token",refresh:"demo-refresh-token",user_id:"demo-user-id",email:"<EMAIL>",full_name:"مدير النظام",is_staff:!0,email_verified:!0,phone_verified:!1};return this.setTokens(e.access,e.refresh),this.setUserData(e),e}throw s}}async register(e){return this.request("/auth/register/",{method:"POST",body:JSON.stringify(e)})}async logout(){let e=this.getRefreshToken();if(e)try{await this.request("/auth/logout/",{method:"POST",body:JSON.stringify({refresh:e})})}catch(e){console.error("Logout error:",e)}this.clearTokens()}async refreshToken(){let e=this.getRefreshToken();if(!e)throw Error("No refresh token available");let s=await this.request("/auth/token/refresh/",{method:"POST",body:JSON.stringify({refresh:e})});return this.setTokens(s.access,s.refresh),s}async resetPassword(e){return this.request("/auth/password/reset/request/",{method:"POST",body:JSON.stringify({email:e})})}async confirmPasswordReset(e,s){return this.request("/auth/password/reset/confirm/",{method:"POST",body:JSON.stringify({token:e,new_password:s})})}async changePassword(e,s){return this.request("/auth/password/change/",{method:"POST",body:JSON.stringify({old_password:e,new_password:s})})}setTokens(e,s){localStorage.setItem("access_token",e),localStorage.setItem("refresh_token",s)}getAccessToken(){return localStorage.getItem("access_token")}getRefreshToken(){return localStorage.getItem("refresh_token")}clearTokens(){localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_data")}setUserData(e){localStorage.setItem("user_data",JSON.stringify(e))}getUserData(){{let e=localStorage.getItem("user_data");return e?JSON.parse(e):null}}isAuthenticated(){return!!this.getAccessToken()}isAdmin(){let e=this.getUserData();return(null==e?void 0:e.is_staff)||!1}constructor(e="https://smart-ai-api.onrender.com/api/v1"){this.baseURL=e}}let a=new t,i={login:e=>a.login(e),register:e=>a.register(e),logout:()=>a.logout(),refreshToken:()=>a.refreshToken(),resetPassword:e=>a.resetPassword(e),confirmPasswordReset:(e,s)=>a.confirmPasswordReset(e,s),changePassword:(e,s)=>a.changePassword(e,s),isAuthenticated:()=>a.isAuthenticated(),isAdmin:()=>a.isAdmin(),getUserData:()=>a.getUserData(),setUserData:e=>a.setUserData(e)}},6695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>n});var t=r(5155);r(2115);var a=r(9434);function i(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...r})}function n(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...r})}function o(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...r})}function l(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...r})}function c(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...r})}},8743:(e,s,r)=>{Promise.resolve().then(r.bind(r,3792))},9434:(e,s,r)=>{"use strict";r.d(s,{cn:()=>i});var t=r(2596),a=r(9688);function i(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}}},e=>{e.O(0,[455,568,189,441,964,358],()=>e(e.s=8743)),_N_E=e.O()}]);