(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{4659:(e,s,l)=>{Promise.resolve().then(l.bind(l,6616))},6616:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>v});var a=l(5155),r=l(2115),t=l(5695),c=l(2177),n=l(221),i=l(8309),d=l(6874),x=l.n(d),m=l(2318),o=l(1007),h=l(8883),j=l(2919),p=l(8749),u=l(2657),N=l(285),f=l(2523),w=l(6695),b=l(7759),g=l(5174);let y=i.Ik({full_name:i.Yj().min(1,"الاسم الكامل مطلوب").min(2,"الاسم يجب أن يكون حرفين على الأقل"),email:i.Yj().min(1,"البريد الإلكتروني مطلوب").email("يرجى إدخال بريد إلكتروني صحيح"),password:i.Yj().min(1,"كلمة المرور مطلوبة").min(8,"كلمة المرور يجب أن تكون 8 أحرف على الأقل").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,"كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم"),password_confirm:i.Yj().min(1,"تأكيد كلمة المرور مطلوب")}).refine(e=>e.password===e.password_confirm,{message:"كلمات المرور غير متطابقة",path:["password_confirm"]});function v(){let e=(0,t.useRouter)(),{register:s,isLoading:l,error:i,isAuthenticated:d,clearError:v}=(0,g.A)(),[_,A]=(0,r.useState)(!1),[C,k]=(0,r.useState)(!1),B=(0,c.mN)({resolver:(0,n.u)(y),defaultValues:{full_name:"",email:"",password:"",password_confirm:""}});(0,r.useEffect)(()=>{d&&e.push("/admin/dashboard")},[d,e]),(0,r.useEffect)(()=>()=>v(),[v]);let R=async l=>{v();let a={full_name:l.full_name,email:l.email,password:l.password,password_confirm:l.password_confirm};await s(a)&&e.push("/login")};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100",children:(0,a.jsx)(m.A,{className:"h-6 w-6 text-green-600"})}),(0,a.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"إنشاء حساب جديد"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"انضم إلى لوحة التحكم الإدارية"})]}),(0,a.jsxs)(w.Zp,{children:[(0,a.jsxs)(w.aR,{className:"space-y-1",children:[(0,a.jsx)(w.ZB,{className:"text-2xl text-center",children:"مرحباً بك"}),(0,a.jsx)(w.BT,{className:"text-center",children:"يرجى إدخال بياناتك لإنشاء حساب جديد"})]}),(0,a.jsx)(w.Wu,{children:(0,a.jsx)(b.lV,{...B,children:(0,a.jsxs)("form",{onSubmit:B.handleSubmit(R),className:"space-y-4",children:[(0,a.jsx)(b.zB,{control:B.control,name:"full_name",render:e=>{let{field:s}=e;return(0,a.jsxs)(b.eI,{children:[(0,a.jsxs)(b.lR,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),"الاسم الكامل"]}),(0,a.jsx)(b.MJ,{children:(0,a.jsx)(f.p,{...s,type:"text",placeholder:"أدخل اسمك الكامل",disabled:l})}),(0,a.jsx)(b.C5,{})]})}}),(0,a.jsx)(b.zB,{control:B.control,name:"email",render:e=>{let{field:s}=e;return(0,a.jsxs)(b.eI,{children:[(0,a.jsxs)(b.lR,{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"البريد الإلكتروني"]}),(0,a.jsx)(b.MJ,{children:(0,a.jsx)(f.p,{...s,type:"email",placeholder:"<EMAIL>",disabled:l,className:"text-right",dir:"ltr"})}),(0,a.jsx)(b.C5,{})]})}}),(0,a.jsx)(b.zB,{control:B.control,name:"password",render:e=>{let{field:s}=e;return(0,a.jsxs)(b.eI,{children:[(0,a.jsxs)(b.lR,{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),"كلمة المرور"]}),(0,a.jsx)(b.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.p,{...s,type:_?"text":"password",placeholder:"••••••••",disabled:l,className:"pr-10"}),(0,a.jsx)("button",{type:"button",onClick:()=>A(!_),className:"absolute inset-y-0 right-0 pr-3 flex items-center",disabled:l,children:_?(0,a.jsx)(p.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(u.A,{className:"h-4 w-4 text-gray-400"})})]})}),(0,a.jsx)(b.C5,{})]})}}),(0,a.jsx)(b.zB,{control:B.control,name:"password_confirm",render:e=>{let{field:s}=e;return(0,a.jsxs)(b.eI,{children:[(0,a.jsxs)(b.lR,{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),"تأكيد كلمة المرور"]}),(0,a.jsx)(b.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.p,{...s,type:C?"text":"password",placeholder:"••••••••",disabled:l,className:"pr-10"}),(0,a.jsx)("button",{type:"button",onClick:()=>k(!C),className:"absolute inset-y-0 right-0 pr-3 flex items-center",disabled:l,children:C?(0,a.jsx)(p.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(u.A,{className:"h-4 w-4 text-gray-400"})})]})}),(0,a.jsx)(b.C5,{})]})}}),i&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-sm text-red-600 text-center",children:i})}),(0,a.jsx)(N.$,{type:"submit",className:"w-full",disabled:l,children:l?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"}),"جاري إنشاء الحساب..."]}):(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),"إنشاء حساب"]})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["لديك حساب بالفعل؟"," ",(0,a.jsx)(x(),{href:"/login",className:"text-blue-600 hover:text-blue-500 font-medium",children:"تسجيل الدخول"})]})})]})})})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"\xa9 2025 لوحة التحكم الإدارية. جميع الحقوق محفوظة."})})]})})}}},e=>{e.O(0,[455,568,874,72,299,571,904,441,964,358],()=>e(e.s=4659)),_N_E=e.O()}]);