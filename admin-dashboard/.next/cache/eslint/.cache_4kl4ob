[{"/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/admin/dashboard/page.tsx": "1", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/admin/layout.tsx": "2", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/components-demo/page.tsx": "3", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/forgot-password/page.tsx": "4", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/layout.tsx": "5", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/login/page.tsx": "6", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/page.tsx": "7", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/register/page.tsx": "8", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/admin/sidebar.tsx": "9", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/auth/protected-route.tsx": "10", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/providers/auth-provider.tsx": "11", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/alert.tsx": "12", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/badge.tsx": "13", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/breadcrumb.tsx": "14", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/button.tsx": "15", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/card.tsx": "16", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/checkbox.tsx": "17", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/data-table.tsx": "18", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/dialog.tsx": "19", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/dropdown-menu.tsx": "20", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/empty-state.tsx": "21", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/error-boundary.tsx": "22", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/form.tsx": "23", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/index.ts": "24", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/input.tsx": "25", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/label.tsx": "26", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/navigation-menu.tsx": "27", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/pagination.tsx": "28", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/radio-group.tsx": "29", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/select.tsx": "30", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/spinner.tsx": "31", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/switch.tsx": "32", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/table.tsx": "33", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/tabs.tsx": "34", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/tooltip.tsx": "35", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/lib/api.ts": "36", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/lib/utils.ts": "37", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/middleware.ts": "38", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/stores/auth.ts": "39", "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/types/index.ts": "40"}, {"size": 9618, "mtime": 1753612095798, "results": "41", "hashOfConfig": "42"}, {"size": 860, "mtime": 1753612130090, "results": "43", "hashOfConfig": "42"}, {"size": 10343, "mtime": 1753611100650, "results": "44", "hashOfConfig": "42"}, {"size": 8708, "mtime": 1753611773570, "results": "45", "hashOfConfig": "42"}, {"size": 1604, "mtime": 1753608714934, "results": "46", "hashOfConfig": "42"}, {"size": 12913, "mtime": 1753613803730, "results": "47", "hashOfConfig": "42"}, {"size": 5170, "mtime": 1753609038590, "results": "48", "hashOfConfig": "42"}, {"size": 10938, "mtime": 1753611779964, "results": "49", "hashOfConfig": "42"}, {"size": 7068, "mtime": 1753612122811, "results": "50", "hashOfConfig": "42"}, {"size": 1387, "mtime": 1753611713887, "results": "51", "hashOfConfig": "42"}, {"size": 386, "mtime": 1753608667191, "results": "52", "hashOfConfig": "42"}, {"size": 4178, "mtime": 1753610767483, "results": "53", "hashOfConfig": "42"}, {"size": 1629, "mtime": 1753610734361, "results": "54", "hashOfConfig": "42"}, {"size": 2729, "mtime": 1753610916574, "results": "55", "hashOfConfig": "42"}, {"size": 2170, "mtime": 1753611413065, "results": "56", "hashOfConfig": "42"}, {"size": 1989, "mtime": 1753608546210, "results": "57", "hashOfConfig": "42"}, {"size": 1026, "mtime": 1753610837567, "results": "58", "hashOfConfig": "42"}, {"size": 7071, "mtime": 1753611451496, "results": "59", "hashOfConfig": "42"}, {"size": 3848, "mtime": 1753610722827, "results": "60", "hashOfConfig": "42"}, {"size": 7320, "mtime": 1753610821134, "results": "61", "hashOfConfig": "42"}, {"size": 3192, "mtime": 1753611001281, "results": "62", "hashOfConfig": "42"}, {"size": 4571, "mtime": 1753611023531, "results": "63", "hashOfConfig": "42"}, {"size": 4202, "mtime": 1753610887716, "results": "64", "hashOfConfig": "42"}, {"size": 703, "mtime": 1753611064011, "results": "65", "hashOfConfig": "42"}, {"size": 967, "mtime": 1753608546201, "results": "66", "hashOfConfig": "42"}, {"size": 611, "mtime": 1753608546205, "results": "67", "hashOfConfig": "42"}, {"size": 5234, "mtime": 1753610958394, "results": "68", "hashOfConfig": "42"}, {"size": 2713, "mtime": 1753611439971, "results": "69", "hashOfConfig": "42"}, {"size": 1437, "mtime": 1753610848091, "results": "70", "hashOfConfig": "42"}, {"size": 5638, "mtime": 1753610704078, "results": "71", "hashOfConfig": "42"}, {"size": 1787, "mtime": 1753610747475, "results": "72", "hashOfConfig": "42"}, {"size": 1155, "mtime": 1753610858232, "results": "73", "hashOfConfig": "42"}, {"size": 2859, "mtime": 1753610981610, "results": "74", "hashOfConfig": "42"}, {"size": 1891, "mtime": 1753610791134, "results": "75", "hashOfConfig": "42"}, {"size": 1142, "mtime": 1753610778299, "results": "76", "hashOfConfig": "42"}, {"size": 8569, "mtime": 1753613190144, "results": "77", "hashOfConfig": "42"}, {"size": 166, "mtime": 1753607820252, "results": "78", "hashOfConfig": "42"}, {"size": 2334, "mtime": 1753608659357, "results": "79", "hashOfConfig": "42"}, {"size": 6027, "mtime": 1753613810315, "results": "80", "hashOfConfig": "42"}, {"size": 5843, "mtime": 1753611801429, "results": "81", "hashOfConfig": "42"}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "mc3tav", {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/admin/dashboard/page.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/admin/layout.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/components-demo/page.tsx", ["202"], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/forgot-password/page.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/layout.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/login/page.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/page.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/register/page.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/admin/sidebar.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/auth/protected-route.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/providers/auth-provider.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/alert.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/badge.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/breadcrumb.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/button.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/card.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/checkbox.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/data-table.tsx", ["203"], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/dialog.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/dropdown-menu.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/empty-state.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/error-boundary.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/form.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/index.ts", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/input.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/label.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/navigation-menu.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/pagination.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/radio-group.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/select.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/spinner.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/switch.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/table.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/tabs.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/ui/tooltip.tsx", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/lib/api.ts", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/lib/utils.ts", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/middleware.ts", ["204"], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/stores/auth.ts", [], [], "/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/types/index.ts", [], [], {"ruleId": "205", "severity": 1, "message": "206", "line": 47, "column": 29, "nodeType": null, "messageId": "207", "endLine": 47, "endColumn": 36}, {"ruleId": "205", "severity": 1, "message": "208", "line": 62, "column": 3, "nodeType": null, "messageId": "207", "endLine": 62, "endColumn": 18}, {"ruleId": "205", "severity": 1, "message": "209", "line": 31, "column": 9, "nodeType": null, "messageId": "207", "endLine": 31, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'Package' is defined but never used.", "unusedVar", "'emptyStateTitle' is assigned a value but never used.", "'isPublicRoute' is assigned a value but never used."]