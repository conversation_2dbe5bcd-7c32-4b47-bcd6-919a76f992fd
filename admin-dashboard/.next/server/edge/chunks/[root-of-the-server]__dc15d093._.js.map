{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\n// Define protected routes that require authentication\nconst protectedRoutes = ['/admin'];\n\n// Define auth routes that should redirect to dashboard if already authenticated\nconst authRoutes = ['/login', '/register', '/forgot-password'];\n\n// Define public routes that don't require authentication\nconst publicRoutes = ['/', '/about', '/contact'];\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n  \n  // Get token from cookies or headers\n  const token = request.cookies.get('access_token')?.value || \n                request.headers.get('authorization')?.replace('Bearer ', '');\n\n  // Check if the current path is protected\n  const isProtectedRoute = protectedRoutes.some(route => \n    pathname.startsWith(route)\n  );\n\n  // Check if the current path is an auth route\n  const isAuthRoute = authRoutes.some(route => \n    pathname.startsWith(route)\n  );\n\n  // Check if the current path is public\n  const isPublicRoute = publicRoutes.some(route => \n    pathname === route || pathname.startsWith(route)\n  );\n\n  // If accessing protected route without token, redirect to login\n  if (isProtectedRoute && !token) {\n    const loginUrl = new URL('/login', request.url);\n    loginUrl.searchParams.set('redirect', pathname);\n    return NextResponse.redirect(loginUrl);\n  }\n\n  // If accessing auth route with token, redirect to welcome page\n  if (isAuthRoute && token) {\n    return NextResponse.redirect(new URL('/admin/welcome', request.url));\n  }\n\n  // If accessing root path, redirect based on authentication\n  if (pathname === '/') {\n    if (token) {\n      return NextResponse.redirect(new URL('/admin/welcome', request.url));\n    } else {\n      return NextResponse.redirect(new URL('/login', request.url));\n    }\n  }\n\n  // Allow the request to continue\n  return NextResponse.next();\n}\n\n// Configure which paths the middleware should run on\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA,sDAAsD;AACtD,MAAM,kBAAkB;IAAC;CAAS;AAElC,gFAAgF;AAChF,MAAM,aAAa;IAAC;IAAU;IAAa;CAAmB;AAE9D,yDAAyD;AACzD,MAAM,eAAe;IAAC;IAAK;IAAU;CAAW;AAEzC,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,oCAAoC;IACpC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB,SACrC,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;IAEvE,yCAAyC;IACzC,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,SAAS,UAAU,CAAC;IAGtB,6CAA6C;IAC7C,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,QAClC,SAAS,UAAU,CAAC;IAGtB,sCAAsC;IACtC,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,QACtC,aAAa,SAAS,SAAS,UAAU,CAAC;IAG5C,gEAAgE;IAChE,IAAI,oBAAoB,CAAC,OAAO;QAC9B,MAAM,WAAW,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC9C,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;QACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,+DAA+D;IAC/D,IAAI,eAAe,OAAO;QACxB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,kBAAkB,QAAQ,GAAG;IACpE;IAEA,2DAA2D;IAC3D,IAAI,aAAa,KAAK;QACpB,IAAI,OAAO;YACT,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,kBAAkB,QAAQ,GAAG;QACpE,OAAO;YACL,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC5D;IACF;IAEA,gCAAgC;IAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}