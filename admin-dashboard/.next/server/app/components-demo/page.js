(()=>{var a={};a.id=329,a.ids=[329],a.modules={13:(a,b,c)=>{"use strict";c.d(b,{J:()=>g});var d=c(687);c(3210);var e=c(8148),f=c(4780);function g({className:a,...b}){return(0,d.jsx)(e.b,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...b})}},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1669:(a,b,c)=>{"use strict";c.d(b,{C5:()=>r,MJ:()=>q,eI:()=>o,lR:()=>p,lV:()=>j,zB:()=>l});var d=c(687),e=c(3210),f=c(8730),g=c(7605),h=c(4780),i=c(13);let j=g.Op,k=e.createContext({}),l=({...a})=>(0,d.jsx)(k.Provider,{value:{name:a.name},children:(0,d.jsx)(g.xI,{...a})}),m=()=>{let a=e.useContext(k),b=e.useContext(n),{getFieldState:c}=(0,g.xW)(),d=(0,g.lN)({name:a.name}),f=c(a.name,d);if(!a)throw Error("useFormField should be used within <FormField>");let{id:h}=b;return{id:h,name:a.name,formItemId:`${h}-form-item`,formDescriptionId:`${h}-form-item-description`,formMessageId:`${h}-form-item-message`,...f}},n=e.createContext({});function o({className:a,...b}){let c=e.useId();return(0,d.jsx)(n.Provider,{value:{id:c},children:(0,d.jsx)("div",{"data-slot":"form-item",className:(0,h.cn)("grid gap-2",a),...b})})}function p({className:a,...b}){let{error:c,formItemId:e}=m();return(0,d.jsx)(i.J,{"data-slot":"form-label","data-error":!!c,className:(0,h.cn)("data-[error=true]:text-destructive",a),htmlFor:e,...b})}function q({...a}){let{error:b,formItemId:c,formDescriptionId:e,formMessageId:g}=m();return(0,d.jsx)(f.DX,{"data-slot":"form-control",id:c,"aria-describedby":b?`${e} ${g}`:`${e}`,"aria-invalid":!!b,...a})}function r({className:a,...b}){let{error:c,formMessageId:e}=m(),f=c?String(c?.message??""):b.children;return f?(0,d.jsx)("p",{"data-slot":"form-message",id:e,className:(0,h.cn)("text-destructive text-sm",a),...b,children:f}):null}},1860:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},2039:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/components-demo/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/components-demo/page.tsx","default")},2892:(a,b,c)=>{Promise.resolve().then(c.bind(c,9598))},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:a=>{"use strict";a.exports=require("path")},4027:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(687);c(3210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(9384),e=c(2348);function f(...a){return(0,e.QP)((0,d.$)(a))}},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6044:(a,b,c)=>{Promise.resolve().then(c.bind(c,2039))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6494:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["components-demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,2039)),"/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/components-demo/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/components-demo/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/components-demo/page",pathname:"/components-demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/components-demo/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6834:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(687);c(3210);var e=c(4224),f=c(4780);let g=(0,e.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",success:"border-transparent bg-success text-success-foreground shadow hover:bg-success/80",warning:"border-transparent bg-warning text-warning-foreground shadow hover:bg-warning/80",info:"border-transparent bg-info text-info-foreground shadow hover:bg-info/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function h({className:a,variant:b,size:c,...e}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b,size:c}),a),...e})}},8354:a=>{"use strict";a.exports=require("util")},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i,r:()=>h});var d=c(687);c(3210);var e=c(8730),f=c(4224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},9598:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>hw});var d,e,f,g=c(687),h=c(3210),i=c.t(h,2),j=c(9523),k=c(9667),l=c(13),m=c(4493),n=c(1215);function o(a,[b,c]){return Math.min(c,Math.max(b,a))}function p(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}function q(a,b=[]){let c=[],d=()=>{let b=c.map(a=>h.createContext(a));return function(c){let d=c?.[a]||b;return h.useMemo(()=>({[`__scope${a}`]:{...c,[a]:d}}),[c,d])}};return d.scopeName=a,[function(b,d){let e=h.createContext(d),f=c.length;c=[...c,d];let i=b=>{let{scope:c,children:d,...i}=b,j=c?.[a]?.[f]||e,k=h.useMemo(()=>i,Object.values(i));return(0,g.jsx)(j.Provider,{value:k,children:d})};return i.displayName=b+"Provider",[i,function(c,g){let i=g?.[a]?.[f]||e,j=h.useContext(i);if(j)return j;if(void 0!==d)return d;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let d=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return h.useMemo(()=>({[`__scope${b.scopeName}`]:d}),[d])}};return c.scopeName=b.scopeName,c}(d,...b)]}var r=c(8599),s=c(8730);function t(a){let b=a+"CollectionProvider",[c,d]=q(b),[e,f]=c(b,{collectionRef:{current:null},itemMap:new Map}),i=a=>{let{scope:b,children:c}=a,d=h.useRef(null),f=h.useRef(new Map).current;return(0,g.jsx)(e,{scope:b,itemMap:f,collectionRef:d,children:c})};i.displayName=b;let j=a+"CollectionSlot",k=(0,s.TL)(j),l=h.forwardRef((a,b)=>{let{scope:c,children:d}=a,e=f(j,c),h=(0,r.s)(b,e.collectionRef);return(0,g.jsx)(k,{ref:h,children:d})});l.displayName=j;let m=a+"CollectionItemSlot",n="data-radix-collection-item",o=(0,s.TL)(m),p=h.forwardRef((a,b)=>{let{scope:c,children:d,...e}=a,i=h.useRef(null),j=(0,r.s)(b,i),k=f(m,c);return h.useEffect(()=>(k.itemMap.set(i,{ref:i,...e}),()=>void k.itemMap.delete(i))),(0,g.jsx)(o,{...{[n]:""},ref:j,children:d})});return p.displayName=m,[{Provider:i,Slot:l,ItemSlot:p},function(b){let c=f(a+"CollectionConsumer",b);return h.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${n}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},d]}var u=new WeakMap;function v(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=w(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function w(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],u.set(this,!0)}set(a,b){return u.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=w(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],l=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{l||k[a-1]!==b||(l=!0);let c=k[l?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=v(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=v(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return v(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}});var x=h.createContext(void 0);function y(a){let b=h.useContext(x);return a||b||"ltr"}var z=c(4163);function A(a){let b=h.useRef(a);return h.useEffect(()=>{b.current=a}),h.useMemo(()=>(...a)=>b.current?.(...a),[])}var B="dismissableLayer.update",C=h.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),D=h.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:i,onInteractOutside:j,onDismiss:k,...l}=a,m=h.useContext(C),[n,o]=h.useState(null),q=n?.ownerDocument??globalThis?.document,[,s]=h.useState({}),t=(0,r.s)(b,a=>o(a)),u=Array.from(m.layers),[v]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),w=u.indexOf(v),x=n?u.indexOf(n):-1,y=m.layersWithOutsidePointerEventsDisabled.size>0,D=x>=w,G=function(a,b=globalThis?.document){let c=A(a),d=h.useRef(!1),e=h.useRef(()=>{});return h.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){F("dismissableLayer.pointerDownOutside",c,f,{discrete:!0})},f={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",e.current),e.current=d,b.addEventListener("click",e.current,{once:!0})):d()}else b.removeEventListener("click",e.current);d.current=!1},f=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(f),b.removeEventListener("pointerdown",a),b.removeEventListener("click",e.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...m.branches].some(a=>a.contains(b));D&&!c&&(f?.(a),j?.(a),a.defaultPrevented||k?.())},q),H=function(a,b=globalThis?.document){let c=A(a),d=h.useRef(!1);return h.useEffect(()=>{let a=a=>{a.target&&!d.current&&F("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...m.branches].some(a=>a.contains(b))&&(i?.(a),j?.(a),a.defaultPrevented||k?.())},q);return!function(a,b=globalThis?.document){let c=A(a);h.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{x===m.layers.size-1&&(d?.(a),!a.defaultPrevented&&k&&(a.preventDefault(),k()))},q),h.useEffect(()=>{if(n)return c&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(e=q.body.style.pointerEvents,q.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(n)),m.layers.add(n),E(),()=>{c&&1===m.layersWithOutsidePointerEventsDisabled.size&&(q.body.style.pointerEvents=e)}},[n,q,c,m]),h.useEffect(()=>()=>{n&&(m.layers.delete(n),m.layersWithOutsidePointerEventsDisabled.delete(n),E())},[n,m]),h.useEffect(()=>{let a=()=>s({});return document.addEventListener(B,a),()=>document.removeEventListener(B,a)},[]),(0,g.jsx)(z.sG.div,{...l,ref:t,style:{pointerEvents:y?D?"auto":"none":void 0,...a.style},onFocusCapture:p(a.onFocusCapture,H.onFocusCapture),onBlurCapture:p(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:p(a.onPointerDownCapture,G.onPointerDownCapture)})});function E(){let a=new CustomEvent(B);document.dispatchEvent(a)}function F(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,z.hO)(e,f):e.dispatchEvent(f)}D.displayName="DismissableLayer",h.forwardRef((a,b)=>{let c=h.useContext(C),d=h.useRef(null),e=(0,r.s)(b,d);return h.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,g.jsx)(z.sG.div,{...a,ref:e})}).displayName="DismissableLayerBranch";var G=0;function H(){h.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??I()),document.body.insertAdjacentElement("beforeend",a[1]??I()),G++,()=>{1===G&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),G--}},[])}function I(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var J="focusScope.autoFocusOnMount",K="focusScope.autoFocusOnUnmount",L={bubbles:!1,cancelable:!0},M=h.forwardRef((a,b)=>{let{loop:c=!1,trapped:d=!1,onMountAutoFocus:e,onUnmountAutoFocus:f,...i}=a,[j,k]=h.useState(null),l=A(e),m=A(f),n=h.useRef(null),o=(0,r.s)(b,a=>k(a)),p=h.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;h.useEffect(()=>{if(d){let a=function(a){if(p.paused||!j)return;let b=a.target;j.contains(b)?n.current=b:P(n.current,{select:!0})},b=function(a){if(p.paused||!j)return;let b=a.relatedTarget;null!==b&&(j.contains(b)||P(n.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&P(j)});return j&&c.observe(j,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[d,j,p.paused]),h.useEffect(()=>{if(j){Q.add(p);let a=document.activeElement;if(!j.contains(a)){let b=new CustomEvent(J,L);j.addEventListener(J,l),j.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(P(d,{select:b}),document.activeElement!==c)return}(N(j).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&P(j))}return()=>{j.removeEventListener(J,l),setTimeout(()=>{let b=new CustomEvent(K,L);j.addEventListener(K,m),j.dispatchEvent(b),b.defaultPrevented||P(a??document.body,{select:!0}),j.removeEventListener(K,m),Q.remove(p)},0)}}},[j,l,m,p]);let q=h.useCallback(a=>{if(!c&&!d||p.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,e=document.activeElement;if(b&&e){let b=a.currentTarget,[d,f]=function(a){let b=N(a);return[O(b,a),O(b.reverse(),a)]}(b);d&&f?a.shiftKey||e!==f?a.shiftKey&&e===d&&(a.preventDefault(),c&&P(f,{select:!0})):(a.preventDefault(),c&&P(d,{select:!0})):e===b&&a.preventDefault()}},[c,d,p.paused]);return(0,g.jsx)(z.sG.div,{tabIndex:-1,...i,ref:o,onKeyDown:q})});function N(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function O(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function P(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}M.displayName="FocusScope";var Q=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=R(a,b)).unshift(b)},remove(b){a=R(a,b),a[0]?.resume()}}}();function R(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}var S=globalThis?.document?h.useLayoutEffect:()=>{},T=i[" useId ".trim().toString()]||(()=>void 0),U=0;function V(a){let[b,c]=h.useState(T());return S(()=>{a||c(a=>a??String(U++))},[a]),a||(b?`radix-${b}`:"")}let W=["top","right","bottom","left"],X=Math.min,Y=Math.max,Z=Math.round,$=Math.floor,_=a=>({x:a,y:a}),aa={left:"right",right:"left",bottom:"top",top:"bottom"},ab={start:"end",end:"start"};function ac(a,b){return"function"==typeof a?a(b):a}function ad(a){return a.split("-")[0]}function ae(a){return a.split("-")[1]}function af(a){return"x"===a?"y":"x"}function ag(a){return"y"===a?"height":"width"}let ah=new Set(["top","bottom"]);function ai(a){return ah.has(ad(a))?"y":"x"}function aj(a){return a.replace(/start|end/g,a=>ab[a])}let ak=["left","right"],al=["right","left"],am=["top","bottom"],an=["bottom","top"];function ao(a){return a.replace(/left|right|bottom|top/g,a=>aa[a])}function ap(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function aq(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function ar(a,b,c){let d,{reference:e,floating:f}=a,g=ai(b),h=af(ai(b)),i=ag(h),j=ad(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,n=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(ae(b)){case"start":d[h]-=n*(c&&k?-1:1);break;case"end":d[h]+=n*(c&&k?-1:1)}return d}let as=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=ar(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=ar(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function at(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:m=!1,padding:n=0}=ac(b,a),o=ap(n),p=h[m?"floating"===l?"reference":"floating":l],q=aq(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(p)))||c?p:p.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),r="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,s=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),t=await (null==f.isElement?void 0:f.isElement(s))&&await (null==f.getScale?void 0:f.getScale(s))||{x:1,y:1},u=aq(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:r,offsetParent:s,strategy:i}):r);return{top:(q.top-u.top+o.top)/t.y,bottom:(u.bottom-q.bottom+o.bottom)/t.y,left:(q.left-u.left+o.left)/t.x,right:(u.right-q.right+o.right)/t.x}}function au(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function av(a){return W.some(b=>a[b]>=0)}let aw=new Set(["left","top"]);async function ax(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=ad(c),h=ae(c),i="y"===ai(c),j=aw.has(g)?-1:1,k=f&&i?-1:1,l=ac(b,a),{mainAxis:m,crossAxis:n,alignmentAxis:o}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof o&&(n="end"===h?-1*o:o),i?{x:n*k,y:m*j}:{x:m*j,y:n*k}}function ay(){return"undefined"!=typeof window}function az(a){return aC(a)?(a.nodeName||"").toLowerCase():"#document"}function aA(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function aB(a){var b;return null==(b=(aC(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function aC(a){return!!ay()&&(a instanceof Node||a instanceof aA(a).Node)}function aD(a){return!!ay()&&(a instanceof Element||a instanceof aA(a).Element)}function aE(a){return!!ay()&&(a instanceof HTMLElement||a instanceof aA(a).HTMLElement)}function aF(a){return!!ay()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof aA(a).ShadowRoot)}let aG=new Set(["inline","contents"]);function aH(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=aS(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!aG.has(e)}let aI=new Set(["table","td","th"]),aJ=[":popover-open",":modal"];function aK(a){return aJ.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let aL=["transform","translate","scale","rotate","perspective"],aM=["transform","translate","scale","rotate","perspective","filter"],aN=["paint","layout","strict","content"];function aO(a){let b=aP(),c=aD(a)?aS(a):a;return aL.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||aM.some(a=>(c.willChange||"").includes(a))||aN.some(a=>(c.contain||"").includes(a))}function aP(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let aQ=new Set(["html","body","#document"]);function aR(a){return aQ.has(az(a))}function aS(a){return aA(a).getComputedStyle(a)}function aT(a){return aD(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function aU(a){if("html"===az(a))return a;let b=a.assignedSlot||a.parentNode||aF(a)&&a.host||aB(a);return aF(b)?b.host:b}function aV(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=aU(b);return aR(c)?b.ownerDocument?b.ownerDocument.body:b.body:aE(c)&&aH(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=aA(e);if(f){let a=aW(g);return b.concat(g,g.visualViewport||[],aH(e)?e:[],a&&c?aV(a):[])}return b.concat(e,aV(e,[],c))}function aW(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function aX(a){let b=aS(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=aE(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,h=Z(c)!==f||Z(d)!==g;return h&&(c=f,d=g),{width:c,height:d,$:h}}function aY(a){return aD(a)?a:a.contextElement}function aZ(a){let b=aY(a);if(!aE(b))return _(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=aX(b),g=(f?Z(c.width):c.width)/d,h=(f?Z(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),h&&Number.isFinite(h)||(h=1),{x:g,y:h}}let a$=_(0);function a_(a){let b=aA(a);return aP()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:a$}function a0(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=aY(a),h=_(1);b&&(d?aD(d)&&(h=aZ(d)):h=aZ(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===aA(g))&&e)?a_(g):_(0),j=(f.left+i.x)/h.x,k=(f.top+i.y)/h.y,l=f.width/h.x,m=f.height/h.y;if(g){let a=aA(g),b=d&&aD(d)?aA(d):d,c=a,e=aW(c);for(;e&&d&&b!==c;){let a=aZ(e),b=e.getBoundingClientRect(),d=aS(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;j*=a.x,k*=a.y,l*=a.x,m*=a.y,j+=f,k+=g,e=aW(c=aA(e))}}return aq({width:l,height:m,x:j,y:k})}function a1(a,b){let c=aT(a).scrollLeft;return b?b.left+c:a0(aB(a)).left+c}function a2(a,b,c){void 0===c&&(c=!1);let d=a.getBoundingClientRect();return{x:d.left+b.scrollLeft-(c?0:a1(a,d)),y:d.top+b.scrollTop}}let a3=new Set(["absolute","fixed"]);function a4(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=aA(a),d=aB(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=aP();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=aB(a),c=aT(a),d=a.ownerDocument.body,e=Y(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=Y(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),g=-c.scrollLeft+a1(a),h=-c.scrollTop;return"rtl"===aS(d).direction&&(g+=Y(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:g,y:h}}(aB(a));else if(aD(b))d=function(a,b){let c=a0(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=aE(a)?aZ(a):_(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=a_(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return aq(d)}function a5(a){return"static"===aS(a).position}function a6(a,b){if(!aE(a)||"fixed"===aS(a).position)return null;if(b)return b(a);let c=a.offsetParent;return aB(a)===c&&(c=c.ownerDocument.body),c}function a7(a,b){var c;let d=aA(a);if(aK(a))return d;if(!aE(a)){let b=aU(a);for(;b&&!aR(b);){if(aD(b)&&!a5(b))return b;b=aU(b)}return d}let e=a6(a,b);for(;e&&(c=e,aI.has(az(c)))&&a5(e);)e=a6(e,b);return e&&aR(e)&&a5(e)&&!aO(e)?d:e||function(a){let b=aU(a);for(;aE(b)&&!aR(b);){if(aO(b))return b;if(aK(b))break;b=aU(b)}return null}(a)||d}let a8=async function(a){let b=this.getOffsetParent||a7,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=aE(b),e=aB(b),f="fixed"===c,g=a0(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=_(0);if(d||!d&&!f)if(("body"!==az(b)||aH(e))&&(h=aT(b)),d){let a=a0(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=a1(e));f&&!d&&e&&(i.x=a1(e));let j=!e||d||f?_(0):a2(e,h);return{x:g.left+h.scrollLeft-i.x-j.x,y:g.top+h.scrollTop-i.y-j.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},a9={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=aB(d),h=!!b&&aK(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},j=_(1),k=_(0),l=aE(d);if((l||!l&&!f)&&(("body"!==az(d)||aH(g))&&(i=aT(d)),aE(d))){let a=a0(d);j=aZ(d),k.x=a.x+d.clientLeft,k.y=a.y+d.clientTop}let m=!g||l||f?_(0):a2(g,i,!0);return{width:c.width*j.x,height:c.height*j.y,x:c.x*j.x-i.scrollLeft*j.x+k.x+m.x,y:c.y*j.y-i.scrollTop*j.y+k.y+m.y}},getDocumentElement:aB,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,f=[..."clippingAncestors"===c?aK(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=aV(a,[],!1).filter(a=>aD(a)&&"body"!==az(a)),e=null,f="fixed"===aS(a).position,g=f?aU(a):a;for(;aD(g)&&!aR(g);){let b=aS(g),c=aO(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&a3.has(e.position)||aH(g)&&!c&&function a(b,c){let d=aU(b);return!(d===c||!aD(d)||aR(d))&&("fixed"===aS(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=aU(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],g=f[0],h=f.reduce((a,c)=>{let d=a4(b,c,e);return a.top=Y(d.top,a.top),a.right=X(d.right,a.right),a.bottom=X(d.bottom,a.bottom),a.left=Y(d.left,a.left),a},a4(b,g,e));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}},getOffsetParent:a7,getElementRects:a8,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=aX(a);return{width:b,height:c}},getScale:aZ,isElement:aD,isRTL:function(a){return"rtl"===aS(a).direction}};function ba(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let bb=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:f,platform:g,elements:h,middlewareData:i}=b,{element:j,padding:k=0}=ac(a,b)||{};if(null==j)return{};let l=ap(k),m={x:c,y:d},n=af(ai(e)),o=ag(n),p=await g.getDimensions(j),q="y"===n,r=q?"clientHeight":"clientWidth",s=f.reference[o]+f.reference[n]-m[n]-f.floating[o],t=m[n]-f.reference[n],u=await (null==g.getOffsetParent?void 0:g.getOffsetParent(j)),v=u?u[r]:0;v&&await (null==g.isElement?void 0:g.isElement(u))||(v=h.floating[r]||f.floating[o]);let w=v/2-p[o]/2-1,x=X(l[q?"top":"left"],w),y=X(l[q?"bottom":"right"],w),z=v-p[o]-y,A=v/2-p[o]/2+(s/2-t/2),B=Y(x,X(A,z)),C=!i.arrow&&null!=ae(e)&&A!==B&&f.reference[o]/2-(A<x?x:y)-p[o]/2<0,D=C?A<x?A-x:A-z:0;return{[n]:m[n]+D,data:{[n]:B,centerOffset:A-B-D,...C&&{alignmentOffset:D}},reset:C}}});var bc="undefined"!=typeof document?h.useLayoutEffect:function(){};function bd(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!bd(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!bd(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function be(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function bf(a,b){let c=be(a);return Math.round(b*c)/c}function bg(a){let b=h.useRef(a);return bc(()=>{b.current=a}),b}var bh=h.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,g.jsx)(z.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,g.jsx)("polygon",{points:"0,0 30,0 15,10"})})});function bi(a){let[b,c]=h.useState(void 0);return S(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}bh.displayName="Arrow";var bj="Popper",[bk,bl]=q(bj),[bm,bn]=bk(bj),bo=a=>{let{__scopePopper:b,children:c}=a,[d,e]=h.useState(null);return(0,g.jsx)(bm,{scope:b,anchor:d,onAnchorChange:e,children:c})};bo.displayName=bj;var bp="PopperAnchor",bq=h.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:d,...e}=a,f=bn(bp,c),i=h.useRef(null),j=(0,r.s)(b,i);return h.useEffect(()=>{f.onAnchorChange(d?.current||i.current)}),d?null:(0,g.jsx)(z.sG.div,{...e,ref:j})});bq.displayName=bp;var br="PopperContent",[bs,bt]=bk(br),bu=h.forwardRef((a,b)=>{let{__scopePopper:c,side:d="bottom",sideOffset:e=0,align:f="center",alignOffset:i=0,arrowPadding:j=0,avoidCollisions:k=!0,collisionBoundary:l=[],collisionPadding:m=0,sticky:o="partial",hideWhenDetached:p=!1,updatePositionStrategy:q="optimized",onPlaced:s,...t}=a,u=bn(br,c),[v,w]=h.useState(null),x=(0,r.s)(b,a=>w(a)),[y,B]=h.useState(null),C=bi(y),D=C?.width??0,E=C?.height??0,F="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},G=Array.isArray(l)?l:[l],H=G.length>0,I={padding:F,boundary:G.filter(by),altBoundary:H},{refs:J,floatingStyles:K,placement:L,isPositioned:M,middlewareData:N}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:d=[],platform:e,elements:{reference:f,floating:g}={},transform:i=!0,whileElementsMounted:j,open:k}=a,[l,m]=h.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[o,p]=h.useState(d);bd(o,d)||p(d);let[q,r]=h.useState(null),[s,t]=h.useState(null),u=h.useCallback(a=>{a!==y.current&&(y.current=a,r(a))},[]),v=h.useCallback(a=>{a!==z.current&&(z.current=a,t(a))},[]),w=f||q,x=g||s,y=h.useRef(null),z=h.useRef(null),A=h.useRef(l),B=null!=j,C=bg(j),D=bg(e),E=bg(k),F=h.useCallback(()=>{if(!y.current||!z.current)return;let a={placement:b,strategy:c,middleware:o};D.current&&(a.platform=D.current),((a,b,c)=>{let d=new Map,e={platform:a9,...c},f={...e.platform,_c:d};return as(a,b,{...e,platform:f})})(y.current,z.current,a).then(a=>{let b={...a,isPositioned:!1!==E.current};G.current&&!bd(A.current,b)&&(A.current=b,n.flushSync(()=>{m(b)}))})},[o,b,c,D,E]);bc(()=>{!1===k&&A.current.isPositioned&&(A.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let G=h.useRef(!1);bc(()=>(G.current=!0,()=>{G.current=!1}),[]),bc(()=>{if(w&&(y.current=w),x&&(z.current=x),w&&x){if(C.current)return C.current(w,x,F);F()}},[w,x,F,C,B]);let H=h.useMemo(()=>({reference:y,floating:z,setReference:u,setFloating:v}),[u,v]),I=h.useMemo(()=>({reference:w,floating:x}),[w,x]),J=h.useMemo(()=>{let a={position:c,left:0,top:0};if(!I.floating)return a;let b=bf(I.floating,l.x),d=bf(I.floating,l.y);return i?{...a,transform:"translate("+b+"px, "+d+"px)",...be(I.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,i,I.floating,l.x,l.y]);return h.useMemo(()=>({...l,update:F,refs:H,elements:I,floatingStyles:J}),[l,F,H,I,J])}({strategy:"fixed",placement:d+("center"!==f?"-"+f:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:j=!1}=d,k=aY(a),l=f||g?[...k?aV(k):[],...aV(b)]:[];l.forEach(a=>{f&&a.addEventListener("scroll",c,{passive:!0}),g&&a.addEventListener("resize",c)});let m=k&&i?function(a,b){let c,d=null,e=aB(a);function f(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function g(h,i){void 0===h&&(h=!1),void 0===i&&(i=1),f();let j=a.getBoundingClientRect(),{left:k,top:l,width:m,height:n}=j;if(h||b(),!m||!n)return;let o=$(l),p=$(e.clientWidth-(k+m)),q={rootMargin:-o+"px "+-p+"px "+-$(e.clientHeight-(l+n))+"px "+-$(k)+"px",threshold:Y(0,X(1,i))||1},r=!0;function s(b){let d=b[0].intersectionRatio;if(d!==i){if(!r)return g();d?g(!1,d):c=setTimeout(()=>{g(!1,1e-7)},1e3)}1!==d||ba(j,a.getBoundingClientRect())||g(),r=!1}try{d=new IntersectionObserver(s,{...q,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(s,q)}d.observe(a)}(!0),f}(k,c):null,n=-1,o=null;h&&(o=new ResizeObserver(a=>{let[d]=a;d&&d.target===k&&o&&(o.unobserve(b),cancelAnimationFrame(n),n=requestAnimationFrame(()=>{var a;null==(a=o)||a.observe(b)})),c()}),k&&!j&&o.observe(k),o.observe(b));let p=j?a0(a):null;return j&&function b(){let d=a0(a);p&&!ba(p,d)&&c(),p=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;l.forEach(a=>{f&&a.removeEventListener("scroll",c),g&&a.removeEventListener("resize",c)}),null==m||m(),null==(a=o)||a.disconnect(),o=null,j&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===q}),elements:{reference:u.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await ax(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:e+E,alignmentAxis:i}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...i}=ac(a,b),j={x:c,y:d},k=await at(b,i),l=ai(ad(e)),m=af(l),n=j[m],o=j[l];if(f){let a="y"===m?"top":"left",b="y"===m?"bottom":"right",c=n+k[a],d=n-k[b];n=Y(c,X(n,d))}if(g){let a="y"===l?"top":"left",b="y"===l?"bottom":"right",c=o+k[a],d=o-k[b];o=Y(c,X(o,d))}let p=h.fn({...b,[m]:n,[l]:o});return{...p,data:{x:p.x-c,y:p.y-d,enabled:{[m]:f,[l]:g}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===o?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=ac(a,b),k={x:c,y:d},l=ai(e),m=af(l),n=k[m],o=k[l],p=ac(h,b),q="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(i){let a="y"===m?"height":"width",b=f.reference[m]-f.floating[a]+q.mainAxis,c=f.reference[m]+f.reference[a]-q.mainAxis;n<b?n=b:n>c&&(n=c)}if(j){var r,s;let a="y"===m?"width":"height",b=aw.has(ad(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(r=g.offset)?void 0:r[l])||0)+(b?0:q.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(s=g.offset)?void 0:s[l])||0)-(b?q.crossAxis:0);o<c?o=c:o>d&&(o=d)}return{[m]:n,[l]:o}}}}(a),options:[a,b]}))():void 0,...I}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:m}=b,{mainAxis:n=!0,crossAxis:o=!0,fallbackPlacements:p,fallbackStrategy:q="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:s=!0,...t}=ac(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let u=ad(h),v=ai(k),w=ad(k)===k,x=await (null==l.isRTL?void 0:l.isRTL(m.floating)),y=p||(w||!s?[ao(k)]:function(a){let b=ao(a);return[aj(a),b,aj(b)]}(k)),z="none"!==r;!p&&z&&y.push(...function(a,b,c,d){let e=ae(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?al:ak;return b?ak:al;case"left":case"right":return b?am:an;default:return[]}}(ad(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(aj)))),f}(k,s,r,x));let A=[k,...y],B=await at(b,t),C=[],D=(null==(d=i.flip)?void 0:d.overflows)||[];if(n&&C.push(B[u]),o){let a=function(a,b,c){void 0===c&&(c=!1);let d=ae(a),e=af(ai(a)),f=ag(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=ao(g)),[g,ao(g)]}(h,j,x);C.push(B[a[0]],B[a[1]])}if(D=[...D,{placement:h,overflows:C}],!C.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=A[a];if(b&&("alignment"!==o||v===ai(b)||D.every(a=>a.overflows[0]>0&&ai(a.placement)===v)))return{data:{index:a,overflows:D},reset:{placement:b}};let c=null==(f=D.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(q){case"bestFit":{let a=null==(g=D.filter(a=>{if(z){let b=ai(a.placement);return b===v||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({...I}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,f,{placement:g,rects:h,platform:i,elements:j}=b,{apply:k=()=>{},...l}=ac(a,b),m=await at(b,l),n=ad(g),o=ae(g),p="y"===ai(g),{width:q,height:r}=h.floating;"top"===n||"bottom"===n?(e=n,f=o===(await (null==i.isRTL?void 0:i.isRTL(j.floating))?"start":"end")?"left":"right"):(f=n,e="end"===o?"top":"bottom");let s=r-m.top-m.bottom,t=q-m.left-m.right,u=X(r-m[e],s),v=X(q-m[f],t),w=!b.middlewareData.shift,x=u,y=v;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(y=t),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(x=s),w&&!o){let a=Y(m.left,0),b=Y(m.right,0),c=Y(m.top,0),d=Y(m.bottom,0);p?y=q-2*(0!==a||0!==b?a+b:Y(m.left,m.right)):x=r-2*(0!==c||0!==d?c+d:Y(m.top,m.bottom))}await k({...b,availableWidth:y,availableHeight:x});let z=await i.getDimensions(j.floating);return q!==z.width||r!==z.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({...I,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),y&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?bb({element:c.current,padding:d}).fn(b):{}:c?bb({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:y,padding:j}),bz({arrowWidth:D,arrowHeight:E}),p&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=ac(a,b);switch(d){case"referenceHidden":{let a=au(await at(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:av(a)}}}case"escaped":{let a=au(await at(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:av(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",...I})]}),[O,P]=bA(L),Q=A(s);S(()=>{M&&Q?.()},[M,Q]);let R=N.arrow?.x,T=N.arrow?.y,U=N.arrow?.centerOffset!==0,[V,W]=h.useState();return S(()=>{v&&W(window.getComputedStyle(v).zIndex)},[v]),(0,g.jsx)("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:M?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:V,"--radix-popper-transform-origin":[N.transformOrigin?.x,N.transformOrigin?.y].join(" "),...N.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,g.jsx)(bs,{scope:c,placedSide:O,onArrowChange:B,arrowX:R,arrowY:T,shouldHideArrow:U,children:(0,g.jsx)(z.sG.div,{"data-side":O,"data-align":P,...t,ref:x,style:{...t.style,animation:M?void 0:"none"}})})})});bu.displayName=br;var bv="PopperArrow",bw={top:"bottom",right:"left",bottom:"top",left:"right"},bx=h.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=bt(bv,c),f=bw[e.placedSide];return(0,g.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,g.jsx)(bh,{...d,ref:b,style:{...d.style,display:"block"}})})});function by(a){return null!==a}bx.displayName=bv;var bz=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=bA(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function bA(a){let[b,c="center"]=a.split("-");return[b,c]}var bB=h.forwardRef((a,b)=>{let{container:c,...d}=a,[e,f]=h.useState(!1);S(()=>f(!0),[]);let i=c||e&&globalThis?.document?.body;return i?n.createPortal((0,g.jsx)(z.sG.div,{...d,ref:b}),i):null});bB.displayName="Portal";var bC=i[" useInsertionEffect ".trim().toString()]||S;function bD({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[e,f,g]=function({defaultProp:a,onChange:b}){let[c,d]=h.useState(a),e=h.useRef(c),f=h.useRef(b);return bC(()=>{f.current=b},[b]),h.useEffect(()=>{e.current!==c&&(f.current?.(c),e.current=c)},[c,e]),[c,d,f]}({defaultProp:b,onChange:c}),i=void 0!==a,j=i?a:e;{let b=h.useRef(void 0!==a);h.useEffect(()=>{let a=b.current;if(a!==i){let b=i?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=i},[i,d])}return[j,h.useCallback(b=>{if(i){let c="function"==typeof b?b(a):b;c!==a&&g.current?.(c)}else f(b)},[i,a,f,g])]}function bE(a){let b=h.useRef({value:a,previous:a});return h.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}Symbol("RADIX:SYNC_STATE");var bF=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),bG=h.forwardRef((a,b)=>(0,g.jsx)(z.sG.span,{...a,ref:b,style:{...bF,...a.style}}));bG.displayName="VisuallyHidden";var bH=new WeakMap,bI=new WeakMap,bJ={},bK=0,bL=function(a){return a&&(a.host||bL(a.parentNode))},bM=function(a,b,c,d){var e=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=bL(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});bJ[c]||(bJ[c]=new WeakMap);var f=bJ[c],g=[],h=new Set,i=new Set(e),j=function(a){!a||h.has(a)||(h.add(a),j(a.parentNode))};e.forEach(j);var k=function(a){!a||i.has(a)||Array.prototype.forEach.call(a.children,function(a){if(h.has(a))k(a);else try{var b=a.getAttribute(d),e=null!==b&&"false"!==b,i=(bH.get(a)||0)+1,j=(f.get(a)||0)+1;bH.set(a,i),f.set(a,j),g.push(a),1===i&&e&&bI.set(a,!0),1===j&&a.setAttribute(c,"true"),e||a.setAttribute(d,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return k(b),h.clear(),bK++,function(){g.forEach(function(a){var b=bH.get(a)-1,e=f.get(a)-1;bH.set(a,b),f.set(a,e),b||(bI.has(a)||a.removeAttribute(d),bI.delete(a)),e||a.removeAttribute(c)}),--bK||(bH=new WeakMap,bH=new WeakMap,bI=new WeakMap,bJ={})}},bN=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),bM(d,e,c,"aria-hidden")):function(){return null}},bO=function(){return(bO=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function bP(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var bQ=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),bR="width-before-scroll-bar";function bS(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var bT="undefined"!=typeof window?h.useLayoutEffect:h.useEffect,bU=new WeakMap;function bV(a){return a}var bW=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=bV),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=bO({async:!0,ssr:!1},a),e}(),bX=function(){},bY=h.forwardRef(function(a,b){var c,d,e,f,g=h.useRef(null),i=h.useState({onScrollCapture:bX,onWheelCapture:bX,onTouchMoveCapture:bX}),j=i[0],k=i[1],l=a.forwardProps,m=a.children,n=a.className,o=a.removeScrollBar,p=a.enabled,q=a.shards,r=a.sideCar,s=a.noRelative,t=a.noIsolation,u=a.inert,v=a.allowPinchZoom,w=a.as,x=a.gapMode,y=bP(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=(c=[g,b],d=function(a){return c.forEach(function(b){return bS(b,a)})},(e=(0,h.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,f=e.facade,bT(function(){var a=bU.get(f);if(a){var b=new Set(a),d=new Set(c),e=f.current;b.forEach(function(a){d.has(a)||bS(a,null)}),d.forEach(function(a){b.has(a)||bS(a,e)})}bU.set(f,c)},[c]),f),A=bO(bO({},y),j);return h.createElement(h.Fragment,null,p&&h.createElement(r,{sideCar:bW,removeScrollBar:o,shards:q,noRelative:s,noIsolation:t,inert:u,setCallbacks:k,allowPinchZoom:!!v,lockRef:g,gapMode:x}),l?h.cloneElement(h.Children.only(m),bO(bO({},A),{ref:z})):h.createElement(void 0===w?"div":w,bO({},A,{className:n,ref:z}),m))});bY.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},bY.classNames={fullWidth:bR,zeroRight:bQ};var bZ=function(a){var b=a.sideCar,c=bP(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return h.createElement(d,bO({},c))};bZ.isSideCarExport=!0;var b$=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=f||c.nc;return b&&a.setAttribute("nonce",b),a}())){var e,g;(e=b).styleSheet?e.styleSheet.cssText=d:e.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},b_=function(){var a=b$();return function(b,c){h.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},b0=function(){var a=b_();return function(b){return a(b.styles,b.dynamic),null}},b1={left:0,top:0,right:0,gap:0},b2=function(a){return parseInt(a||"",10)||0},b3=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[b2(c),b2(d),b2(e)]},b4=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return b1;var b=b3(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},b5=b0(),b6="data-scroll-locked",b7=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(b6,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(bQ," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bR," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bQ," .").concat(bQ," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(bR," .").concat(bR," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(b6,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},b8=function(){var a=parseInt(document.body.getAttribute(b6)||"0",10);return isFinite(a)?a:0},b9=function(){h.useEffect(function(){return document.body.setAttribute(b6,(b8()+1).toString()),function(){var a=b8()-1;a<=0?document.body.removeAttribute(b6):document.body.setAttribute(b6,a.toString())}},[])},ca=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;b9();var f=h.useMemo(function(){return b4(e)},[e]);return h.createElement(b5,{styles:b7(f,!b,e,c?"":"!important")})},cb=!1;if("undefined"!=typeof window)try{var cc=Object.defineProperty({},"passive",{get:function(){return cb=!0,!0}});window.addEventListener("test",cc,cc),window.removeEventListener("test",cc,cc)}catch(a){cb=!1}var cd=!!cb&&{passive:!1},ce=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},cf=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),cg(a,d)){var e=ch(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},cg=function(a,b){return"v"===a?ce(b,"overflowY"):ce(b,"overflowX")},ch=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},ci=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=ch(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&cg(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},cj=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},ck=function(a){return[a.deltaX,a.deltaY]},cl=function(a){return a&&"current"in a?a.current:a},cm=0,cn=[];let co=(d=function(a){var b=h.useRef([]),c=h.useRef([0,0]),d=h.useRef(),e=h.useState(cm++)[0],f=h.useState(b0)[0],g=h.useRef(a);h.useEffect(function(){g.current=a},[a]),h.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(cl),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=h.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!g.current.allowPinchZoom;var e,f=cj(a),h=c.current,i="deltaX"in a?a.deltaX:h[0]-f[0],j="deltaY"in a?a.deltaY:h[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=cf(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=cf(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return ci(n,b,a,"h"===n?i:j,!0)},[]),j=h.useCallback(function(a){if(cn.length&&cn[cn.length-1]===f){var c="deltaY"in a?ck(a):cj(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(g.current.shards||[]).map(cl).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!g.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=h.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=h.useCallback(function(a){c.current=cj(a),d.current=void 0},[]),m=h.useCallback(function(b){k(b.type,ck(b),b.target,i(b,a.lockRef.current))},[]),n=h.useCallback(function(b){k(b.type,cj(b),b.target,i(b,a.lockRef.current))},[]);h.useEffect(function(){return cn.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,cd),document.addEventListener("touchmove",j,cd),document.addEventListener("touchstart",l,cd),function(){cn=cn.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,cd),document.removeEventListener("touchmove",j,cd),document.removeEventListener("touchstart",l,cd)}},[]);var o=a.removeScrollBar,p=a.inert;return h.createElement(h.Fragment,null,p?h.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?h.createElement(ca,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},bW.useMedium(d),bZ);var cp=h.forwardRef(function(a,b){return h.createElement(bY,bO({},a,{ref:b,sideCar:co}))});cp.classNames=bY.classNames;var cq=[" ","Enter","ArrowUp","ArrowDown"],cr=[" ","Enter"],cs="Select",[ct,cu,cv]=t(cs),[cw,cx]=q(cs,[cv,bl]),cy=bl(),[cz,cA]=cw(cs),[cB,cC]=cw(cs),cD=a=>{let{__scopeSelect:b,children:c,open:d,defaultOpen:e,onOpenChange:f,value:i,defaultValue:j,onValueChange:k,dir:l,name:m,autoComplete:n,disabled:o,required:p,form:q}=a,r=cy(b),[s,t]=h.useState(null),[u,v]=h.useState(null),[w,x]=h.useState(!1),z=y(l),[A,B]=bD({prop:d,defaultProp:e??!1,onChange:f,caller:cs}),[C,D]=bD({prop:i,defaultProp:j,onChange:k,caller:cs}),E=h.useRef(null),F=!s||q||!!s.closest("form"),[G,H]=h.useState(new Set),I=Array.from(G).map(a=>a.props.value).join(";");return(0,g.jsx)(bo,{...r,children:(0,g.jsxs)(cz,{required:p,scope:b,trigger:s,onTriggerChange:t,valueNode:u,onValueNodeChange:v,valueNodeHasChildren:w,onValueNodeHasChildrenChange:x,contentId:V(),value:C,onValueChange:D,open:A,onOpenChange:B,dir:z,triggerPointerDownPosRef:E,disabled:o,children:[(0,g.jsx)(ct.Provider,{scope:b,children:(0,g.jsx)(cB,{scope:a.__scopeSelect,onNativeOptionAdd:h.useCallback(a=>{H(b=>new Set(b).add(a))},[]),onNativeOptionRemove:h.useCallback(a=>{H(b=>{let c=new Set(b);return c.delete(a),c})},[]),children:c})}),F?(0,g.jsxs)(de,{"aria-hidden":!0,required:p,tabIndex:-1,name:m,autoComplete:n,value:C,onChange:a=>D(a.target.value),disabled:o,form:q,children:[void 0===C?(0,g.jsx)("option",{value:""}):null,Array.from(G)]},I):null]})})};cD.displayName=cs;var cE="SelectTrigger",cF=h.forwardRef((a,b)=>{let{__scopeSelect:c,disabled:d=!1,...e}=a,f=cy(c),i=cA(cE,c),j=i.disabled||d,k=(0,r.s)(b,i.onTriggerChange),l=cu(c),m=h.useRef("touch"),[n,o,q]=dg(a=>{let b=l().filter(a=>!a.disabled),c=b.find(a=>a.value===i.value),d=dh(b,a,c);void 0!==d&&i.onValueChange(d.value)}),s=a=>{j||(i.onOpenChange(!0),q()),a&&(i.triggerPointerDownPosRef.current={x:Math.round(a.pageX),y:Math.round(a.pageY)})};return(0,g.jsx)(bq,{asChild:!0,...f,children:(0,g.jsx)(z.sG.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:j,"data-disabled":j?"":void 0,"data-placeholder":df(i.value)?"":void 0,...e,ref:k,onClick:p(e.onClick,a=>{a.currentTarget.focus(),"mouse"!==m.current&&s(a)}),onPointerDown:p(e.onPointerDown,a=>{m.current=a.pointerType;let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),0===a.button&&!1===a.ctrlKey&&"mouse"===a.pointerType&&(s(a),a.preventDefault())}),onKeyDown:p(e.onKeyDown,a=>{let b=""!==n.current;a.ctrlKey||a.altKey||a.metaKey||1!==a.key.length||o(a.key),(!b||" "!==a.key)&&cq.includes(a.key)&&(s(),a.preventDefault())})})})});cF.displayName=cE;var cG="SelectValue",cH=h.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,children:f,placeholder:h="",...i}=a,j=cA(cG,c),{onValueNodeHasChildrenChange:k}=j,l=void 0!==f,m=(0,r.s)(b,j.onValueNodeChange);return S(()=>{k(l)},[k,l]),(0,g.jsx)(z.sG.span,{...i,ref:m,style:{pointerEvents:"none"},children:df(j.value)?(0,g.jsx)(g.Fragment,{children:h}):f})});cH.displayName=cG;var cI=h.forwardRef((a,b)=>{let{__scopeSelect:c,children:d,...e}=a;return(0,g.jsx)(z.sG.span,{"aria-hidden":!0,...e,ref:b,children:d||"▼"})});cI.displayName="SelectIcon";var cJ=a=>(0,g.jsx)(bB,{asChild:!0,...a});cJ.displayName="SelectPortal";var cK="SelectContent",cL=h.forwardRef((a,b)=>{let c=cA(cK,a.__scopeSelect),[d,e]=h.useState();return(S(()=>{e(new DocumentFragment)},[]),c.open)?(0,g.jsx)(cP,{...a,ref:b}):d?n.createPortal((0,g.jsx)(cM,{scope:a.__scopeSelect,children:(0,g.jsx)(ct.Slot,{scope:a.__scopeSelect,children:(0,g.jsx)("div",{children:a.children})})}),d):null});cL.displayName=cK;var[cM,cN]=cw(cK),cO=(0,s.TL)("SelectContent.RemoveScroll"),cP=h.forwardRef((a,b)=>{let{__scopeSelect:c,position:d="item-aligned",onCloseAutoFocus:e,onEscapeKeyDown:f,onPointerDownOutside:i,side:j,sideOffset:k,align:l,alignOffset:m,arrowPadding:n,collisionBoundary:o,collisionPadding:q,sticky:s,hideWhenDetached:t,avoidCollisions:u,...v}=a,w=cA(cK,c),[x,y]=h.useState(null),[z,A]=h.useState(null),B=(0,r.s)(b,a=>y(a)),[C,E]=h.useState(null),[F,G]=h.useState(null),I=cu(c),[J,K]=h.useState(!1),L=h.useRef(!1);h.useEffect(()=>{if(x)return bN(x)},[x]),H();let N=h.useCallback(a=>{let[b,...c]=I().map(a=>a.ref.current),[d]=c.slice(-1),e=document.activeElement;for(let c of a)if(c===e||(c?.scrollIntoView({block:"nearest"}),c===b&&z&&(z.scrollTop=0),c===d&&z&&(z.scrollTop=z.scrollHeight),c?.focus(),document.activeElement!==e))return},[I,z]),O=h.useCallback(()=>N([C,x]),[N,C,x]);h.useEffect(()=>{J&&O()},[J,O]);let{onOpenChange:P,triggerPointerDownPosRef:Q}=w;h.useEffect(()=>{if(x){let a={x:0,y:0},b=b=>{a={x:Math.abs(Math.round(b.pageX)-(Q.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(Q.current?.y??0))}},c=c=>{a.x<=10&&a.y<=10?c.preventDefault():x.contains(c.target)||P(!1),document.removeEventListener("pointermove",b),Q.current=null};return null!==Q.current&&(document.addEventListener("pointermove",b),document.addEventListener("pointerup",c,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",b),document.removeEventListener("pointerup",c,{capture:!0})}}},[x,P,Q]),h.useEffect(()=>{let a=()=>P(!1);return window.addEventListener("blur",a),window.addEventListener("resize",a),()=>{window.removeEventListener("blur",a),window.removeEventListener("resize",a)}},[P]);let[R,S]=dg(a=>{let b=I().filter(a=>!a.disabled),c=b.find(a=>a.ref.current===document.activeElement),d=dh(b,a,c);d&&setTimeout(()=>d.ref.current.focus())}),T=h.useCallback((a,b,c)=>{let d=!L.current&&!c;(void 0!==w.value&&w.value===b||d)&&(E(a),d&&(L.current=!0))},[w.value]),U=h.useCallback(()=>x?.focus(),[x]),V=h.useCallback((a,b,c)=>{let d=!L.current&&!c;(void 0!==w.value&&w.value===b||d)&&G(a)},[w.value]),W="popper"===d?cR:cQ,X=W===cR?{side:j,sideOffset:k,align:l,alignOffset:m,arrowPadding:n,collisionBoundary:o,collisionPadding:q,sticky:s,hideWhenDetached:t,avoidCollisions:u}:{};return(0,g.jsx)(cM,{scope:c,content:x,viewport:z,onViewportChange:A,itemRefCallback:T,selectedItem:C,onItemLeave:U,itemTextRefCallback:V,focusSelectedItem:O,selectedItemText:F,position:d,isPositioned:J,searchRef:R,children:(0,g.jsx)(cp,{as:cO,allowPinchZoom:!0,children:(0,g.jsx)(M,{asChild:!0,trapped:w.open,onMountAutoFocus:a=>{a.preventDefault()},onUnmountAutoFocus:p(e,a=>{w.trigger?.focus({preventScroll:!0}),a.preventDefault()}),children:(0,g.jsx)(D,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:f,onPointerDownOutside:i,onFocusOutside:a=>a.preventDefault(),onDismiss:()=>w.onOpenChange(!1),children:(0,g.jsx)(W,{role:"listbox",id:w.contentId,"data-state":w.open?"open":"closed",dir:w.dir,onContextMenu:a=>a.preventDefault(),...v,...X,onPlaced:()=>K(!0),ref:B,style:{display:"flex",flexDirection:"column",outline:"none",...v.style},onKeyDown:p(v.onKeyDown,a=>{let b=a.ctrlKey||a.altKey||a.metaKey;if("Tab"===a.key&&a.preventDefault(),b||1!==a.key.length||S(a.key),["ArrowUp","ArrowDown","Home","End"].includes(a.key)){let b=I().filter(a=>!a.disabled).map(a=>a.ref.current);if(["ArrowUp","End"].includes(a.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(a.key)){let c=a.target,d=b.indexOf(c);b=b.slice(d+1)}setTimeout(()=>N(b)),a.preventDefault()}})})})})})})});cP.displayName="SelectContentImpl";var cQ=h.forwardRef((a,b)=>{let{__scopeSelect:c,onPlaced:d,...e}=a,f=cA(cK,c),i=cN(cK,c),[j,k]=h.useState(null),[l,m]=h.useState(null),n=(0,r.s)(b,a=>m(a)),p=cu(c),q=h.useRef(!1),s=h.useRef(!0),{viewport:t,selectedItem:u,selectedItemText:v,focusSelectedItem:w}=i,x=h.useCallback(()=>{if(f.trigger&&f.valueNode&&j&&l&&t&&u&&v){let a=f.trigger.getBoundingClientRect(),b=l.getBoundingClientRect(),c=f.valueNode.getBoundingClientRect(),e=v.getBoundingClientRect();if("rtl"!==f.dir){let d=e.left-b.left,f=c.left-d,g=a.left-f,h=a.width+g,i=Math.max(h,b.width),k=o(f,[10,Math.max(10,window.innerWidth-10-i)]);j.style.minWidth=h+"px",j.style.left=k+"px"}else{let d=b.right-e.right,f=window.innerWidth-c.right-d,g=window.innerWidth-a.right-f,h=a.width+g,i=Math.max(h,b.width),k=o(f,[10,Math.max(10,window.innerWidth-10-i)]);j.style.minWidth=h+"px",j.style.right=k+"px"}let g=p(),h=window.innerHeight-20,i=t.scrollHeight,k=window.getComputedStyle(l),m=parseInt(k.borderTopWidth,10),n=parseInt(k.paddingTop,10),r=parseInt(k.borderBottomWidth,10),s=m+n+i+parseInt(k.paddingBottom,10)+r,w=Math.min(5*u.offsetHeight,s),x=window.getComputedStyle(t),y=parseInt(x.paddingTop,10),z=parseInt(x.paddingBottom,10),A=a.top+a.height/2-10,B=u.offsetHeight/2,C=m+n+(u.offsetTop+B);if(C<=A){let a=g.length>0&&u===g[g.length-1].ref.current;j.style.bottom="0px";let b=Math.max(h-A,B+(a?z:0)+(l.clientHeight-t.offsetTop-t.offsetHeight)+r);j.style.height=C+b+"px"}else{let a=g.length>0&&u===g[0].ref.current;j.style.top="0px";let b=Math.max(A,m+t.offsetTop+(a?y:0)+B);j.style.height=b+(s-C)+"px",t.scrollTop=C-A+t.offsetTop}j.style.margin="10px 0",j.style.minHeight=w+"px",j.style.maxHeight=h+"px",d?.(),requestAnimationFrame(()=>q.current=!0)}},[p,f.trigger,f.valueNode,j,l,t,u,v,f.dir,d]);S(()=>x(),[x]);let[y,A]=h.useState();S(()=>{l&&A(window.getComputedStyle(l).zIndex)},[l]);let B=h.useCallback(a=>{a&&!0===s.current&&(x(),w?.(),s.current=!1)},[x,w]);return(0,g.jsx)(cS,{scope:c,contentWrapper:j,shouldExpandOnScrollRef:q,onScrollButtonChange:B,children:(0,g.jsx)("div",{ref:k,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:y},children:(0,g.jsx)(z.sG.div,{...e,ref:n,style:{boxSizing:"border-box",maxHeight:"100%",...e.style}})})})});cQ.displayName="SelectItemAlignedPosition";var cR=h.forwardRef((a,b)=>{let{__scopeSelect:c,align:d="start",collisionPadding:e=10,...f}=a,h=cy(c);return(0,g.jsx)(bu,{...h,...f,ref:b,align:d,collisionPadding:e,style:{boxSizing:"border-box",...f.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});cR.displayName="SelectPopperPosition";var[cS,cT]=cw(cK,{}),cU="SelectViewport",cV=h.forwardRef((a,b)=>{let{__scopeSelect:c,nonce:d,...e}=a,f=cN(cU,c),i=cT(cU,c),j=(0,r.s)(b,f.onViewportChange),k=h.useRef(0);return(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:d}),(0,g.jsx)(ct.Slot,{scope:c,children:(0,g.jsx)(z.sG.div,{"data-radix-select-viewport":"",role:"presentation",...e,ref:j,style:{position:"relative",flex:1,overflow:"hidden auto",...e.style},onScroll:p(e.onScroll,a=>{let b=a.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=i;if(d?.current&&c){let a=Math.abs(k.current-b.scrollTop);if(a>0){let d=window.innerHeight-20,e=Math.max(parseFloat(c.style.minHeight),parseFloat(c.style.height));if(e<d){let f=e+a,g=Math.min(d,f),h=f-g;c.style.height=g+"px","0px"===c.style.bottom&&(b.scrollTop=h>0?h:0,c.style.justifyContent="flex-end")}}}k.current=b.scrollTop})})})]})});cV.displayName=cU;var cW="SelectGroup",[cX,cY]=cw(cW);h.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=V();return(0,g.jsx)(cX,{scope:c,id:e,children:(0,g.jsx)(z.sG.div,{role:"group","aria-labelledby":e,...d,ref:b})})}).displayName=cW;var cZ="SelectLabel",c$=h.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=cY(cZ,c);return(0,g.jsx)(z.sG.div,{id:e.id,...d,ref:b})});c$.displayName=cZ;var c_="SelectItem",[c0,c1]=cw(c_),c2=h.forwardRef((a,b)=>{let{__scopeSelect:c,value:d,disabled:e=!1,textValue:f,...i}=a,j=cA(c_,c),k=cN(c_,c),l=j.value===d,[m,n]=h.useState(f??""),[o,q]=h.useState(!1),s=(0,r.s)(b,a=>k.itemRefCallback?.(a,d,e)),t=V(),u=h.useRef("touch"),v=()=>{e||(j.onValueChange(d),j.onOpenChange(!1))};if(""===d)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,g.jsx)(c0,{scope:c,value:d,disabled:e,textId:t,isSelected:l,onItemTextChange:h.useCallback(a=>{n(b=>b||(a?.textContent??"").trim())},[]),children:(0,g.jsx)(ct.ItemSlot,{scope:c,value:d,disabled:e,textValue:m,children:(0,g.jsx)(z.sG.div,{role:"option","aria-labelledby":t,"data-highlighted":o?"":void 0,"aria-selected":l&&o,"data-state":l?"checked":"unchecked","aria-disabled":e||void 0,"data-disabled":e?"":void 0,tabIndex:e?void 0:-1,...i,ref:s,onFocus:p(i.onFocus,()=>q(!0)),onBlur:p(i.onBlur,()=>q(!1)),onClick:p(i.onClick,()=>{"mouse"!==u.current&&v()}),onPointerUp:p(i.onPointerUp,()=>{"mouse"===u.current&&v()}),onPointerDown:p(i.onPointerDown,a=>{u.current=a.pointerType}),onPointerMove:p(i.onPointerMove,a=>{u.current=a.pointerType,e?k.onItemLeave?.():"mouse"===u.current&&a.currentTarget.focus({preventScroll:!0})}),onPointerLeave:p(i.onPointerLeave,a=>{a.currentTarget===document.activeElement&&k.onItemLeave?.()}),onKeyDown:p(i.onKeyDown,a=>{(k.searchRef?.current===""||" "!==a.key)&&(cr.includes(a.key)&&v()," "===a.key&&a.preventDefault())})})})})});c2.displayName=c_;var c3="SelectItemText",c4=h.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,...f}=a,i=cA(c3,c),j=cN(c3,c),k=c1(c3,c),l=cC(c3,c),[m,o]=h.useState(null),p=(0,r.s)(b,a=>o(a),k.onItemTextChange,a=>j.itemTextRefCallback?.(a,k.value,k.disabled)),q=m?.textContent,s=h.useMemo(()=>(0,g.jsx)("option",{value:k.value,disabled:k.disabled,children:q},k.value),[k.disabled,k.value,q]),{onNativeOptionAdd:t,onNativeOptionRemove:u}=l;return S(()=>(t(s),()=>u(s)),[t,u,s]),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(z.sG.span,{id:k.textId,...f,ref:p}),k.isSelected&&i.valueNode&&!i.valueNodeHasChildren?n.createPortal(f.children,i.valueNode):null]})});c4.displayName=c3;var c5="SelectItemIndicator",c6=h.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return c1(c5,c).isSelected?(0,g.jsx)(z.sG.span,{"aria-hidden":!0,...d,ref:b}):null});c6.displayName=c5;var c7="SelectScrollUpButton",c8=h.forwardRef((a,b)=>{let c=cN(c7,a.__scopeSelect),d=cT(c7,a.__scopeSelect),[e,f]=h.useState(!1),i=(0,r.s)(b,d.onScrollButtonChange);return S(()=>{if(c.viewport&&c.isPositioned){let a=function(){f(b.scrollTop>0)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),e?(0,g.jsx)(db,{...a,ref:i,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop-b.offsetHeight)}}):null});c8.displayName=c7;var c9="SelectScrollDownButton",da=h.forwardRef((a,b)=>{let c=cN(c9,a.__scopeSelect),d=cT(c9,a.__scopeSelect),[e,f]=h.useState(!1),i=(0,r.s)(b,d.onScrollButtonChange);return S(()=>{if(c.viewport&&c.isPositioned){let a=function(){let a=b.scrollHeight-b.clientHeight;f(Math.ceil(b.scrollTop)<a)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),e?(0,g.jsx)(db,{...a,ref:i,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop+b.offsetHeight)}}):null});da.displayName=c9;var db=h.forwardRef((a,b)=>{let{__scopeSelect:c,onAutoScroll:d,...e}=a,f=cN("SelectScrollButton",c),i=h.useRef(null),j=cu(c),k=h.useCallback(()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)},[]);return h.useEffect(()=>()=>k(),[k]),S(()=>{let a=j().find(a=>a.ref.current===document.activeElement);a?.ref.current?.scrollIntoView({block:"nearest"})},[j]),(0,g.jsx)(z.sG.div,{"aria-hidden":!0,...e,ref:b,style:{flexShrink:0,...e.style},onPointerDown:p(e.onPointerDown,()=>{null===i.current&&(i.current=window.setInterval(d,50))}),onPointerMove:p(e.onPointerMove,()=>{f.onItemLeave?.(),null===i.current&&(i.current=window.setInterval(d,50))}),onPointerLeave:p(e.onPointerLeave,()=>{k()})})}),dc=h.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return(0,g.jsx)(z.sG.div,{"aria-hidden":!0,...d,ref:b})});dc.displayName="SelectSeparator";var dd="SelectArrow";h.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=cy(c),f=cA(dd,c),h=cN(dd,c);return f.open&&"popper"===h.position?(0,g.jsx)(bx,{...e,...d,ref:b}):null}).displayName=dd;var de=h.forwardRef(({__scopeSelect:a,value:b,...c},d)=>{let e=h.useRef(null),f=(0,r.s)(d,e),i=bE(b);return h.useEffect(()=>{let a=e.current;if(!a)return;let c=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==b&&c){let d=new Event("change",{bubbles:!0});c.call(a,b),a.dispatchEvent(d)}},[i,b]),(0,g.jsx)(z.sG.select,{...c,style:{...bF,...c.style},ref:f,defaultValue:b})});function df(a){return""===a||void 0===a}function dg(a){let b=A(a),c=h.useRef(""),d=h.useRef(0),e=h.useCallback(a=>{let e=c.current+a;b(e),function a(b){c.current=b,window.clearTimeout(d.current),""!==b&&(d.current=window.setTimeout(()=>a(""),1e3))}(e)},[b]),f=h.useCallback(()=>{c.current="",window.clearTimeout(d.current)},[]);return h.useEffect(()=>()=>window.clearTimeout(d.current),[]),[c,e,f]}function dh(a,b,c){var d,e;let f=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,g=c?a.indexOf(c):-1,h=(d=a,e=Math.max(g,0),d.map((a,b)=>d[(e+b)%d.length]));1===f.length&&(h=h.filter(a=>a!==c));let i=h.find(a=>a.textValue.toLowerCase().startsWith(f.toLowerCase()));return i!==c?i:void 0}de.displayName="SelectBubbleInput";var di=c(2688);let dj=(0,di.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),dk=(0,di.A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),dl=(0,di.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var dm=c(4780);let dn=h.forwardRef(({className:a,children:b,...c},d)=>(0,g.jsxs)(cF,{ref:d,className:(0,dm.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border bg-background px-3 py-2 text-sm shadow-xs ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,g.jsx)(cI,{asChild:!0,children:(0,g.jsx)(dj,{className:"h-4 w-4 opacity-50"})})]}));dn.displayName=cF.displayName;let dp=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(c8,{ref:c,className:(0,dm.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,g.jsx)(dk,{className:"h-4 w-4"})}));dp.displayName=c8.displayName;let dq=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(da,{ref:c,className:(0,dm.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,g.jsx)(dj,{className:"h-4 w-4"})}));dq.displayName=da.displayName;let dr=h.forwardRef(({className:a,children:b,position:c="popper",...d},e)=>(0,g.jsx)(cJ,{children:(0,g.jsxs)(cL,{ref:e,className:(0,dm.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...d,children:[(0,g.jsx)(dp,{}),(0,g.jsx)(cV,{className:(0,dm.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,g.jsx)(dq,{})]})}));dr.displayName=cL.displayName,h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(c$,{ref:c,className:(0,dm.cn)("px-2 py-1.5 text-sm font-semibold",a),...b})).displayName=c$.displayName;let ds=h.forwardRef(({className:a,children:b,...c},d)=>(0,g.jsxs)(c2,{ref:d,className:(0,dm.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,g.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,g.jsx)(c6,{children:(0,g.jsx)(dl,{className:"h-4 w-4"})})}),(0,g.jsx)(c4,{children:b})]}));ds.displayName=c2.displayName,h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(dc,{ref:c,className:(0,dm.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=dc.displayName;var dt=a=>{let{present:b,children:c}=a,d=function(a){var b,c;let[d,e]=h.useState(),f=h.useRef(null),g=h.useRef(a),i=h.useRef("none"),[j,k]=(b=a?"mounted":"unmounted",c={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},h.useReducer((a,b)=>c[a][b]??a,b));return h.useEffect(()=>{let a=du(f.current);i.current="mounted"===j?a:"none"},[j]),S(()=>{let b=f.current,c=g.current;if(c!==a){let d=i.current,e=du(b);a?k("MOUNT"):"none"===e||b?.display==="none"?k("UNMOUNT"):c&&d!==e?k("ANIMATION_OUT"):k("UNMOUNT"),g.current=a}},[a,k]),S(()=>{if(d){let a,b=d.ownerDocument.defaultView??window,c=c=>{let e=du(f.current).includes(c.animationName);if(c.target===d&&e&&(k("ANIMATION_END"),!g.current)){let c=d.style.animationFillMode;d.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===d.style.animationFillMode&&(d.style.animationFillMode=c)})}},e=a=>{a.target===d&&(i.current=du(f.current))};return d.addEventListener("animationstart",e),d.addEventListener("animationcancel",c),d.addEventListener("animationend",c),()=>{b.clearTimeout(a),d.removeEventListener("animationstart",e),d.removeEventListener("animationcancel",c),d.removeEventListener("animationend",c)}}k("ANIMATION_END")},[d,k]),{isPresent:["mounted","unmountSuspended"].includes(j),ref:h.useCallback(a=>{f.current=a?getComputedStyle(a):null,e(a)},[])}}(b),e="function"==typeof c?c({present:d.isPresent}):h.Children.only(c),f=(0,r.s)(d.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(e));return"function"==typeof c||d.isPresent?h.cloneElement(e,{ref:f}):null};function du(a){return a?.animationName||"none"}dt.displayName="Presence";var dv="Dialog",[dw,dx]=q(dv),[dy,dz]=dw(dv),dA=a=>{let{__scopeDialog:b,children:c,open:d,defaultOpen:e,onOpenChange:f,modal:i=!0}=a,j=h.useRef(null),k=h.useRef(null),[l,m]=bD({prop:d,defaultProp:e??!1,onChange:f,caller:dv});return(0,g.jsx)(dy,{scope:b,triggerRef:j,contentRef:k,contentId:V(),titleId:V(),descriptionId:V(),open:l,onOpenChange:m,onOpenToggle:h.useCallback(()=>m(a=>!a),[m]),modal:i,children:c})};dA.displayName=dv;var dB="DialogTrigger",dC=h.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=dz(dB,c),f=(0,r.s)(b,e.triggerRef);return(0,g.jsx)(z.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":e.open,"aria-controls":e.contentId,"data-state":dW(e.open),...d,ref:f,onClick:p(a.onClick,e.onOpenToggle)})});dC.displayName=dB;var dD="DialogPortal",[dE,dF]=dw(dD,{forceMount:void 0}),dG=a=>{let{__scopeDialog:b,forceMount:c,children:d,container:e}=a,f=dz(dD,b);return(0,g.jsx)(dE,{scope:b,forceMount:c,children:h.Children.map(d,a=>(0,g.jsx)(dt,{present:c||f.open,children:(0,g.jsx)(bB,{asChild:!0,container:e,children:a})}))})};dG.displayName=dD;var dH="DialogOverlay",dI=h.forwardRef((a,b)=>{let c=dF(dH,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=dz(dH,a.__scopeDialog);return f.modal?(0,g.jsx)(dt,{present:d||f.open,children:(0,g.jsx)(dK,{...e,ref:b})}):null});dI.displayName=dH;var dJ=(0,s.TL)("DialogOverlay.RemoveScroll"),dK=h.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=dz(dH,c);return(0,g.jsx)(cp,{as:dJ,allowPinchZoom:!0,shards:[e.contentRef],children:(0,g.jsx)(z.sG.div,{"data-state":dW(e.open),...d,ref:b,style:{pointerEvents:"auto",...d.style}})})}),dL="DialogContent",dM=h.forwardRef((a,b)=>{let c=dF(dL,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=dz(dL,a.__scopeDialog);return(0,g.jsx)(dt,{present:d||f.open,children:f.modal?(0,g.jsx)(dN,{...e,ref:b}):(0,g.jsx)(dO,{...e,ref:b})})});dM.displayName=dL;var dN=h.forwardRef((a,b)=>{let c=dz(dL,a.__scopeDialog),d=h.useRef(null),e=(0,r.s)(b,c.contentRef,d);return h.useEffect(()=>{let a=d.current;if(a)return bN(a)},[]),(0,g.jsx)(dP,{...a,ref:e,trapFocus:c.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:p(a.onCloseAutoFocus,a=>{a.preventDefault(),c.triggerRef.current?.focus()}),onPointerDownOutside:p(a.onPointerDownOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey;(2===b.button||c)&&a.preventDefault()}),onFocusOutside:p(a.onFocusOutside,a=>a.preventDefault())})}),dO=h.forwardRef((a,b)=>{let c=dz(dL,a.__scopeDialog),d=h.useRef(!1),e=h.useRef(!1);return(0,g.jsx)(dP,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:b=>{a.onCloseAutoFocus?.(b),b.defaultPrevented||(d.current||c.triggerRef.current?.focus(),b.preventDefault()),d.current=!1,e.current=!1},onInteractOutside:b=>{a.onInteractOutside?.(b),b.defaultPrevented||(d.current=!0,"pointerdown"===b.detail.originalEvent.type&&(e.current=!0));let f=b.target;c.triggerRef.current?.contains(f)&&b.preventDefault(),"focusin"===b.detail.originalEvent.type&&e.current&&b.preventDefault()}})}),dP=h.forwardRef((a,b)=>{let{__scopeDialog:c,trapFocus:d,onOpenAutoFocus:e,onCloseAutoFocus:f,...i}=a,j=dz(dL,c),k=h.useRef(null),l=(0,r.s)(b,k);return H(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(M,{asChild:!0,loop:!0,trapped:d,onMountAutoFocus:e,onUnmountAutoFocus:f,children:(0,g.jsx)(D,{role:"dialog",id:j.contentId,"aria-describedby":j.descriptionId,"aria-labelledby":j.titleId,"data-state":dW(j.open),...i,ref:l,onDismiss:()=>j.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(d$,{titleId:j.titleId}),(0,g.jsx)(d_,{contentRef:k,descriptionId:j.descriptionId})]})]})}),dQ="DialogTitle",dR=h.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=dz(dQ,c);return(0,g.jsx)(z.sG.h2,{id:e.titleId,...d,ref:b})});dR.displayName=dQ;var dS="DialogDescription",dT=h.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=dz(dS,c);return(0,g.jsx)(z.sG.p,{id:e.descriptionId,...d,ref:b})});dT.displayName=dS;var dU="DialogClose",dV=h.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=dz(dU,c);return(0,g.jsx)(z.sG.button,{type:"button",...d,ref:b,onClick:p(a.onClick,()=>e.onOpenChange(!1))})});function dW(a){return a?"open":"closed"}dV.displayName=dU;var dX="DialogTitleWarning",[dY,dZ]=function(a,b){let c=h.createContext(b),d=a=>{let{children:b,...d}=a,e=h.useMemo(()=>d,Object.values(d));return(0,g.jsx)(c.Provider,{value:e,children:b})};return d.displayName=a+"Provider",[d,function(d){let e=h.useContext(c);if(e)return e;if(void 0!==b)return b;throw Error(`\`${d}\` must be used within \`${a}\``)}]}(dX,{contentName:dL,titleName:dQ,docsSlug:"dialog"}),d$=({titleId:a})=>{let b=dZ(dX),c=`\`${b.contentName}\` requires a \`${b.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${b.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${b.docsSlug}`;return h.useEffect(()=>{a&&(document.getElementById(a)||console.error(c))},[c,a]),null},d_=({contentRef:a,descriptionId:b})=>{let c=dZ("DialogDescriptionWarning"),d=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${c.contentName}}.`;return h.useEffect(()=>{let c=a.current?.getAttribute("aria-describedby");b&&c&&(document.getElementById(b)||console.warn(d))},[d,a,b]),null},d0=c(1860);let d1=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(dI,{ref:c,className:(0,dm.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...b}));d1.displayName=dI.displayName;let d2=h.forwardRef(({className:a,children:b,...c},d)=>(0,g.jsxs)(dG,{children:[(0,g.jsx)(d1,{}),(0,g.jsxs)(dM,{ref:d,className:(0,dm.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[b,(0,g.jsxs)(dV,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,g.jsx)(d0.A,{className:"h-4 w-4"}),(0,g.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));d2.displayName=dM.displayName;let d3=({className:a,...b})=>(0,g.jsx)("div",{className:(0,dm.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...b});d3.displayName="DialogHeader";let d4=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(dR,{ref:c,className:(0,dm.cn)("text-lg font-semibold leading-none tracking-tight",a),...b}));d4.displayName=dR.displayName;let d5=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(dT,{ref:c,className:(0,dm.cn)("text-sm text-muted-foreground",a),...b}));d5.displayName=dT.displayName;var d6=c(6834),d7=c(4224);let d8=(0,d7.F)("animate-spin rounded-full border-2 border-current border-t-transparent",{variants:{size:{sm:"h-4 w-4",default:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},variant:{default:"text-primary",secondary:"text-secondary",muted:"text-muted-foreground",white:"text-white"}},defaultVariants:{size:"default",variant:"default"}});function d9({className:a,size:b,variant:c,label:d,...e}){return(0,g.jsx)("div",{className:(0,dm.cn)("flex items-center justify-center",a),...e,children:(0,g.jsx)("div",{className:(0,dm.cn)(d8({size:b,variant:c})),role:"status","aria-label":d||"Loading",children:(0,g.jsx)("span",{className:"sr-only",children:d||"Loading..."})})})}function ea({text:a="Loading...",showText:b=!0,className:c,size:d,variant:e,...f}){return(0,g.jsxs)("div",{className:(0,dm.cn)("flex flex-col items-center justify-center gap-2",c),...f,children:[(0,g.jsx)(d9,{size:d,variant:e}),b&&(0,g.jsx)("p",{className:"text-sm text-muted-foreground",children:a})]})}var eb=c(5336);let ec=(0,di.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),ed=(0,di.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),ee=(0,di.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),ef=(0,d7.F)("relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",success:"border-success/50 text-success dark:border-success [&>svg]:text-success",warning:"border-warning/50 text-warning dark:border-warning [&>svg]:text-warning",info:"border-info/50 text-info dark:border-info [&>svg]:text-info"}},defaultVariants:{variant:"default"}}),eg=h.forwardRef(({className:a,variant:b,dismissible:c,onDismiss:d,children:e,...f},i)=>{let[j,k]=h.useState(!0);return j?(0,g.jsxs)("div",{ref:i,role:"alert",className:(0,dm.cn)(ef({variant:b}),a),...f,children:[e,c&&(0,g.jsxs)("button",{onClick:()=>{k(!1),d?.()},className:"absolute right-2 top-2 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",children:[(0,g.jsx)(d0.A,{className:"h-4 w-4"}),(0,g.jsx)("span",{className:"sr-only",children:"Close"})]})]}):null});eg.displayName="Alert";let eh=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("h5",{ref:c,className:(0,dm.cn)("mb-1 font-medium leading-none tracking-tight",a),...b}));eh.displayName="AlertTitle";let ei=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("div",{ref:c,className:(0,dm.cn)("text-sm [&_p]:leading-relaxed",a),...b}));ei.displayName="AlertDescription",h.forwardRef(({children:a,...b},c)=>(0,g.jsxs)(eg,{ref:c,variant:"success",...b,children:[(0,g.jsx)(eb.A,{className:"h-4 w-4"}),a]})).displayName="AlertSuccess",h.forwardRef(({children:a,...b},c)=>(0,g.jsxs)(eg,{ref:c,variant:"destructive",...b,children:[(0,g.jsx)(ec,{className:"h-4 w-4"}),a]})).displayName="AlertError",h.forwardRef(({children:a,...b},c)=>(0,g.jsxs)(eg,{ref:c,variant:"warning",...b,children:[(0,g.jsx)(ed,{className:"h-4 w-4"}),a]})).displayName="AlertWarning",h.forwardRef(({children:a,...b},c)=>(0,g.jsxs)(eg,{ref:c,variant:"info",...b,children:[(0,g.jsx)(ee,{className:"h-4 w-4"}),a]})).displayName="AlertInfo";var[ej,ek]=q("Tooltip",[bl]),el=bl(),em="TooltipProvider",en="tooltip.open",[eo,ep]=ej(em),eq=a=>{let{__scopeTooltip:b,delayDuration:c=700,skipDelayDuration:d=300,disableHoverableContent:e=!1,children:f}=a,i=h.useRef(!0),j=h.useRef(!1),k=h.useRef(0);return h.useEffect(()=>{let a=k.current;return()=>window.clearTimeout(a)},[]),(0,g.jsx)(eo,{scope:b,isOpenDelayedRef:i,delayDuration:c,onOpen:h.useCallback(()=>{window.clearTimeout(k.current),i.current=!1},[]),onClose:h.useCallback(()=>{window.clearTimeout(k.current),k.current=window.setTimeout(()=>i.current=!0,d)},[d]),isPointerInTransitRef:j,onPointerInTransitChange:h.useCallback(a=>{j.current=a},[]),disableHoverableContent:e,children:f})};eq.displayName=em;var er="Tooltip",[es,et]=ej(er),eu=a=>{let{__scopeTooltip:b,children:c,open:d,defaultOpen:e,onOpenChange:f,disableHoverableContent:i,delayDuration:j}=a,k=ep(er,a.__scopeTooltip),l=el(b),[m,n]=h.useState(null),o=V(),p=h.useRef(0),q=i??k.disableHoverableContent,r=j??k.delayDuration,s=h.useRef(!1),[t,u]=bD({prop:d,defaultProp:e??!1,onChange:a=>{a?(k.onOpen(),document.dispatchEvent(new CustomEvent(en))):k.onClose(),f?.(a)},caller:er}),v=h.useMemo(()=>t?s.current?"delayed-open":"instant-open":"closed",[t]),w=h.useCallback(()=>{window.clearTimeout(p.current),p.current=0,s.current=!1,u(!0)},[u]),x=h.useCallback(()=>{window.clearTimeout(p.current),p.current=0,u(!1)},[u]),y=h.useCallback(()=>{window.clearTimeout(p.current),p.current=window.setTimeout(()=>{s.current=!0,u(!0),p.current=0},r)},[r,u]);return h.useEffect(()=>()=>{p.current&&(window.clearTimeout(p.current),p.current=0)},[]),(0,g.jsx)(bo,{...l,children:(0,g.jsx)(es,{scope:b,contentId:o,open:t,stateAttribute:v,trigger:m,onTriggerChange:n,onTriggerEnter:h.useCallback(()=>{k.isOpenDelayedRef.current?y():w()},[k.isOpenDelayedRef,y,w]),onTriggerLeave:h.useCallback(()=>{q?x():(window.clearTimeout(p.current),p.current=0)},[x,q]),onOpen:w,onClose:x,disableHoverableContent:q,children:c})})};eu.displayName=er;var ev="TooltipTrigger",ew=h.forwardRef((a,b)=>{let{__scopeTooltip:c,...d}=a,e=et(ev,c),f=ep(ev,c),i=el(c),j=h.useRef(null),k=(0,r.s)(b,j,e.onTriggerChange),l=h.useRef(!1),m=h.useRef(!1),n=h.useCallback(()=>l.current=!1,[]);return h.useEffect(()=>()=>document.removeEventListener("pointerup",n),[n]),(0,g.jsx)(bq,{asChild:!0,...i,children:(0,g.jsx)(z.sG.button,{"aria-describedby":e.open?e.contentId:void 0,"data-state":e.stateAttribute,...d,ref:k,onPointerMove:p(a.onPointerMove,a=>{"touch"!==a.pointerType&&(m.current||f.isPointerInTransitRef.current||(e.onTriggerEnter(),m.current=!0))}),onPointerLeave:p(a.onPointerLeave,()=>{e.onTriggerLeave(),m.current=!1}),onPointerDown:p(a.onPointerDown,()=>{e.open&&e.onClose(),l.current=!0,document.addEventListener("pointerup",n,{once:!0})}),onFocus:p(a.onFocus,()=>{l.current||e.onOpen()}),onBlur:p(a.onBlur,e.onClose),onClick:p(a.onClick,e.onClose)})})});ew.displayName=ev;var[ex,ey]=ej("TooltipPortal",{forceMount:void 0}),ez="TooltipContent",eA=h.forwardRef((a,b)=>{let c=ey(ez,a.__scopeTooltip),{forceMount:d=c.forceMount,side:e="top",...f}=a,h=et(ez,a.__scopeTooltip);return(0,g.jsx)(dt,{present:d||h.open,children:h.disableHoverableContent?(0,g.jsx)(eF,{side:e,...f,ref:b}):(0,g.jsx)(eB,{side:e,...f,ref:b})})}),eB=h.forwardRef((a,b)=>{let c=et(ez,a.__scopeTooltip),d=ep(ez,a.__scopeTooltip),e=h.useRef(null),f=(0,r.s)(b,e),[i,j]=h.useState(null),{trigger:k,onClose:l}=c,m=e.current,{onPointerInTransitChange:n}=d,o=h.useCallback(()=>{j(null),n(!1)},[n]),p=h.useCallback((a,b)=>{let c=a.currentTarget,d={x:a.clientX,y:a.clientY},e=function(a,b){let c=Math.abs(b.top-a.y),d=Math.abs(b.bottom-a.y),e=Math.abs(b.right-a.x),f=Math.abs(b.left-a.x);switch(Math.min(c,d,e,f)){case f:return"left";case e:return"right";case c:return"top";case d:return"bottom";default:throw Error("unreachable")}}(d,c.getBoundingClientRect());j(function(a){let b=a.slice();return b.sort((a,b)=>a.x<b.x?-1:a.x>b.x?1:a.y<b.y?-1:1*!!(a.y>b.y)),function(a){if(a.length<=1)return a.slice();let b=[];for(let c=0;c<a.length;c++){let d=a[c];for(;b.length>=2;){let a=b[b.length-1],c=b[b.length-2];if((a.x-c.x)*(d.y-c.y)>=(a.y-c.y)*(d.x-c.x))b.pop();else break}b.push(d)}b.pop();let c=[];for(let b=a.length-1;b>=0;b--){let d=a[b];for(;c.length>=2;){let a=c[c.length-1],b=c[c.length-2];if((a.x-b.x)*(d.y-b.y)>=(a.y-b.y)*(d.x-b.x))c.pop();else break}c.push(d)}return(c.pop(),1===b.length&&1===c.length&&b[0].x===c[0].x&&b[0].y===c[0].y)?b:b.concat(c)}(b)}([...function(a,b,c=5){let d=[];switch(b){case"top":d.push({x:a.x-c,y:a.y+c},{x:a.x+c,y:a.y+c});break;case"bottom":d.push({x:a.x-c,y:a.y-c},{x:a.x+c,y:a.y-c});break;case"left":d.push({x:a.x+c,y:a.y-c},{x:a.x+c,y:a.y+c});break;case"right":d.push({x:a.x-c,y:a.y-c},{x:a.x-c,y:a.y+c})}return d}(d,e),...function(a){let{top:b,right:c,bottom:d,left:e}=a;return[{x:e,y:b},{x:c,y:b},{x:c,y:d},{x:e,y:d}]}(b.getBoundingClientRect())])),n(!0)},[n]);return h.useEffect(()=>()=>o(),[o]),h.useEffect(()=>{if(k&&m){let a=a=>p(a,m),b=a=>p(a,k);return k.addEventListener("pointerleave",a),m.addEventListener("pointerleave",b),()=>{k.removeEventListener("pointerleave",a),m.removeEventListener("pointerleave",b)}}},[k,m,p,o]),h.useEffect(()=>{if(i){let a=a=>{let b=a.target,c={x:a.clientX,y:a.clientY},d=k?.contains(b)||m?.contains(b),e=!function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}(c,i);d?o():e&&(o(),l())};return document.addEventListener("pointermove",a),()=>document.removeEventListener("pointermove",a)}},[k,m,i,l,o]),(0,g.jsx)(eF,{...a,ref:f})}),[eC,eD]=ej(er,{isInside:!1}),eE=(0,s.Dc)("TooltipContent"),eF=h.forwardRef((a,b)=>{let{__scopeTooltip:c,children:d,"aria-label":e,onEscapeKeyDown:f,onPointerDownOutside:i,...j}=a,k=et(ez,c),l=el(c),{onClose:m}=k;return h.useEffect(()=>(document.addEventListener(en,m),()=>document.removeEventListener(en,m)),[m]),h.useEffect(()=>{if(k.trigger){let a=a=>{let b=a.target;b?.contains(k.trigger)&&m()};return window.addEventListener("scroll",a,{capture:!0}),()=>window.removeEventListener("scroll",a,{capture:!0})}},[k.trigger,m]),(0,g.jsx)(D,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:f,onPointerDownOutside:i,onFocusOutside:a=>a.preventDefault(),onDismiss:m,children:(0,g.jsxs)(bu,{"data-state":k.stateAttribute,...l,...j,ref:b,style:{...j.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,g.jsx)(eE,{children:d}),(0,g.jsx)(eC,{scope:c,isInside:!0,children:(0,g.jsx)(bG,{id:k.contentId,role:"tooltip",children:e||d})})]})})});eA.displayName=ez;var eG="TooltipArrow";h.forwardRef((a,b)=>{let{__scopeTooltip:c,...d}=a,e=el(c);return eD(eG,c).isInside?null:(0,g.jsx)(bx,{...e,...d,ref:b})}).displayName=eG;let eH=h.forwardRef(({className:a,sideOffset:b=4,...c},d)=>(0,g.jsx)(eA,{ref:d,sideOffset:b,className:(0,dm.cn)("z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...c}));eH.displayName=eA.displayName;var eI="rovingFocusGroup.onEntryFocus",eJ={bubbles:!1,cancelable:!0},eK="RovingFocusGroup",[eL,eM,eN]=t(eK),[eO,eP]=q(eK,[eN]),[eQ,eR]=eO(eK),eS=h.forwardRef((a,b)=>(0,g.jsx)(eL.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,g.jsx)(eL.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,g.jsx)(eT,{...a,ref:b})})}));eS.displayName=eK;var eT=h.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:d,loop:e=!1,dir:f,currentTabStopId:i,defaultCurrentTabStopId:j,onCurrentTabStopIdChange:k,onEntryFocus:l,preventScrollOnEntryFocus:m=!1,...n}=a,o=h.useRef(null),q=(0,r.s)(b,o),s=y(f),[t,u]=bD({prop:i,defaultProp:j??null,onChange:k,caller:eK}),[v,w]=h.useState(!1),x=A(l),B=eM(c),C=h.useRef(!1),[D,E]=h.useState(0);return h.useEffect(()=>{let a=o.current;if(a)return a.addEventListener(eI,x),()=>a.removeEventListener(eI,x)},[x]),(0,g.jsx)(eQ,{scope:c,orientation:d,dir:s,loop:e,currentTabStopId:t,onItemFocus:h.useCallback(a=>u(a),[u]),onItemShiftTab:h.useCallback(()=>w(!0),[]),onFocusableItemAdd:h.useCallback(()=>E(a=>a+1),[]),onFocusableItemRemove:h.useCallback(()=>E(a=>a-1),[]),children:(0,g.jsx)(z.sG.div,{tabIndex:v||0===D?-1:0,"data-orientation":d,...n,ref:q,style:{outline:"none",...a.style},onMouseDown:p(a.onMouseDown,()=>{C.current=!0}),onFocus:p(a.onFocus,a=>{let b=!C.current;if(a.target===a.currentTarget&&b&&!v){let b=new CustomEvent(eI,eJ);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=B().filter(a=>a.focusable);eX([a.find(a=>a.active),a.find(a=>a.id===t),...a].filter(Boolean).map(a=>a.ref.current),m)}}C.current=!1}),onBlur:p(a.onBlur,()=>w(!1))})})}),eU="RovingFocusGroupItem",eV=h.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:d=!0,active:e=!1,tabStopId:f,children:i,...j}=a,k=V(),l=f||k,m=eR(eU,c),n=m.currentTabStopId===l,o=eM(c),{onFocusableItemAdd:q,onFocusableItemRemove:r,currentTabStopId:s}=m;return h.useEffect(()=>{if(d)return q(),()=>r()},[d,q,r]),(0,g.jsx)(eL.ItemSlot,{scope:c,id:l,focusable:d,active:e,children:(0,g.jsx)(z.sG.span,{tabIndex:n?0:-1,"data-orientation":m.orientation,...j,ref:b,onMouseDown:p(a.onMouseDown,a=>{d?m.onItemFocus(l):a.preventDefault()}),onFocus:p(a.onFocus,()=>m.onItemFocus(l)),onKeyDown:p(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void m.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return eW[e]}(a,m.orientation,m.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=o().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=m.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>eX(c))}}),children:"function"==typeof i?i({isCurrentTabStop:n,hasTabStop:null!=s}):i})})});eV.displayName=eU;var eW={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function eX(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var eY="Tabs",[eZ,e$]=q(eY,[eP]),e_=eP(),[e0,e1]=eZ(eY),e2=h.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,onValueChange:e,defaultValue:f,orientation:h="horizontal",dir:i,activationMode:j="automatic",...k}=a,l=y(i),[m,n]=bD({prop:d,onChange:e,defaultProp:f??"",caller:eY});return(0,g.jsx)(e0,{scope:c,baseId:V(),value:m,onValueChange:n,orientation:h,dir:l,activationMode:j,children:(0,g.jsx)(z.sG.div,{dir:l,"data-orientation":h,...k,ref:b})})});e2.displayName=eY;var e3="TabsList",e4=h.forwardRef((a,b)=>{let{__scopeTabs:c,loop:d=!0,...e}=a,f=e1(e3,c),h=e_(c);return(0,g.jsx)(eS,{asChild:!0,...h,orientation:f.orientation,dir:f.dir,loop:d,children:(0,g.jsx)(z.sG.div,{role:"tablist","aria-orientation":f.orientation,...e,ref:b})})});e4.displayName=e3;var e5="TabsTrigger",e6=h.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,disabled:e=!1,...f}=a,h=e1(e5,c),i=e_(c),j=e9(h.baseId,d),k=fa(h.baseId,d),l=d===h.value;return(0,g.jsx)(eV,{asChild:!0,...i,focusable:!e,active:l,children:(0,g.jsx)(z.sG.button,{type:"button",role:"tab","aria-selected":l,"aria-controls":k,"data-state":l?"active":"inactive","data-disabled":e?"":void 0,disabled:e,id:j,...f,ref:b,onMouseDown:p(a.onMouseDown,a=>{e||0!==a.button||!1!==a.ctrlKey?a.preventDefault():h.onValueChange(d)}),onKeyDown:p(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&h.onValueChange(d)}),onFocus:p(a.onFocus,()=>{let a="manual"!==h.activationMode;l||e||!a||h.onValueChange(d)})})})});e6.displayName=e5;var e7="TabsContent",e8=h.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,forceMount:e,children:f,...i}=a,j=e1(e7,c),k=e9(j.baseId,d),l=fa(j.baseId,d),m=d===j.value,n=h.useRef(m);return h.useEffect(()=>{let a=requestAnimationFrame(()=>n.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,g.jsx)(dt,{present:e||m,children:({present:c})=>(0,g.jsx)(z.sG.div,{"data-state":m?"active":"inactive","data-orientation":j.orientation,role:"tabpanel","aria-labelledby":k,hidden:!c,id:l,tabIndex:0,...i,ref:b,style:{...a.style,animationDuration:n.current?"0s":void 0},children:c&&f})})});function e9(a,b){return`${a}-trigger-${b}`}function fa(a,b){return`${a}-content-${b}`}e8.displayName=e7;let fb=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(e4,{ref:c,className:(0,dm.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",a),...b}));fb.displayName=e4.displayName;let fc=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(e6,{ref:c,className:(0,dm.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",a),...b}));fc.displayName=e6.displayName;let fd=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(e8,{ref:c,className:(0,dm.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...b}));fd.displayName=e8.displayName;var fe=["Enter"," "],ff=["ArrowUp","PageDown","End"],fg=["ArrowDown","PageUp","Home",...ff],fh={ltr:[...fe,"ArrowRight"],rtl:[...fe,"ArrowLeft"]},fi={ltr:["ArrowLeft"],rtl:["ArrowRight"]},fj="Menu",[fk,fl,fm]=t(fj),[fn,fo]=q(fj,[fm,bl,eP]),fp=bl(),fq=eP(),[fr,fs]=fn(fj),[ft,fu]=fn(fj);var fv=h.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=fp(c);return(0,g.jsx)(bq,{...e,...d,ref:b})});fv.displayName="MenuAnchor";var fw="MenuPortal",[fx,fy]=fn(fw,{forceMount:void 0}),fz=a=>{let{__scopeMenu:b,forceMount:c,children:d,container:e}=a,f=fs(fw,b);return(0,g.jsx)(fx,{scope:b,forceMount:c,children:(0,g.jsx)(dt,{present:c||f.open,children:(0,g.jsx)(bB,{asChild:!0,container:e,children:d})})})};fz.displayName=fw;var fA="MenuContent",[fB,fC]=fn(fA),fD=h.forwardRef((a,b)=>{let c=fy(fA,a.__scopeMenu),{forceMount:d=c.forceMount,...e}=a,f=fs(fA,a.__scopeMenu),h=fu(fA,a.__scopeMenu);return(0,g.jsx)(fk.Provider,{scope:a.__scopeMenu,children:(0,g.jsx)(dt,{present:d||f.open,children:(0,g.jsx)(fk.Slot,{scope:a.__scopeMenu,children:h.modal?(0,g.jsx)(fE,{...e,ref:b}):(0,g.jsx)(fF,{...e,ref:b})})})})}),fE=h.forwardRef((a,b)=>{let c=fs(fA,a.__scopeMenu),d=h.useRef(null),e=(0,r.s)(b,d);return h.useEffect(()=>{let a=d.current;if(a)return bN(a)},[]),(0,g.jsx)(fH,{...a,ref:e,trapFocus:c.open,disableOutsidePointerEvents:c.open,disableOutsideScroll:!0,onFocusOutside:p(a.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>c.onOpenChange(!1)})}),fF=h.forwardRef((a,b)=>{let c=fs(fA,a.__scopeMenu);return(0,g.jsx)(fH,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>c.onOpenChange(!1)})}),fG=(0,s.TL)("MenuContent.ScrollLock"),fH=h.forwardRef((a,b)=>{let{__scopeMenu:c,loop:d=!1,trapFocus:e,onOpenAutoFocus:f,onCloseAutoFocus:i,disableOutsidePointerEvents:j,onEntryFocus:k,onEscapeKeyDown:l,onPointerDownOutside:m,onFocusOutside:n,onInteractOutside:o,onDismiss:q,disableOutsideScroll:s,...t}=a,u=fs(fA,c),v=fu(fA,c),w=fp(c),x=fq(c),y=fl(c),[z,A]=h.useState(null),B=h.useRef(null),C=(0,r.s)(b,B,u.onContentChange),E=h.useRef(0),F=h.useRef(""),G=h.useRef(0),I=h.useRef(null),J=h.useRef("right"),K=h.useRef(0),L=s?cp:h.Fragment;h.useEffect(()=>()=>window.clearTimeout(E.current),[]),H();let N=h.useCallback(a=>J.current===I.current?.side&&function(a,b){return!!b&&function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}({x:a.clientX,y:a.clientY},b)}(a,I.current?.area),[]);return(0,g.jsx)(fB,{scope:c,searchRef:F,onItemEnter:h.useCallback(a=>{N(a)&&a.preventDefault()},[N]),onItemLeave:h.useCallback(a=>{N(a)||(B.current?.focus(),A(null))},[N]),onTriggerLeave:h.useCallback(a=>{N(a)&&a.preventDefault()},[N]),pointerGraceTimerRef:G,onPointerGraceIntentChange:h.useCallback(a=>{I.current=a},[]),children:(0,g.jsx)(L,{...s?{as:fG,allowPinchZoom:!0}:void 0,children:(0,g.jsx)(M,{asChild:!0,trapped:e,onMountAutoFocus:p(f,a=>{a.preventDefault(),B.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:(0,g.jsx)(D,{asChild:!0,disableOutsidePointerEvents:j,onEscapeKeyDown:l,onPointerDownOutside:m,onFocusOutside:n,onInteractOutside:o,onDismiss:q,children:(0,g.jsx)(eS,{asChild:!0,...x,dir:v.dir,orientation:"vertical",loop:d,currentTabStopId:z,onCurrentTabStopIdChange:A,onEntryFocus:p(k,a=>{v.isUsingKeyboardRef.current||a.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,g.jsx)(bu,{role:"menu","aria-orientation":"vertical","data-state":f5(u.open),"data-radix-menu-content":"",dir:v.dir,...w,...t,ref:C,style:{outline:"none",...t.style},onKeyDown:p(t.onKeyDown,a=>{let b=a.target.closest("[data-radix-menu-content]")===a.currentTarget,c=a.ctrlKey||a.altKey||a.metaKey,d=1===a.key.length;b&&("Tab"===a.key&&a.preventDefault(),!c&&d&&(a=>{let b=F.current+a,c=y().filter(a=>!a.disabled),d=document.activeElement,e=c.find(a=>a.ref.current===d)?.textValue,f=function(a,b,c){var d;let e=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,f=c?a.indexOf(c):-1,g=(d=Math.max(f,0),a.map((b,c)=>a[(d+c)%a.length]));1===e.length&&(g=g.filter(a=>a!==c));let h=g.find(a=>a.toLowerCase().startsWith(e.toLowerCase()));return h!==c?h:void 0}(c.map(a=>a.textValue),b,e),g=c.find(a=>a.textValue===f)?.ref.current;!function a(b){F.current=b,window.clearTimeout(E.current),""!==b&&(E.current=window.setTimeout(()=>a(""),1e3))}(b),g&&setTimeout(()=>g.focus())})(a.key));let e=B.current;if(a.target!==e||!fg.includes(a.key))return;a.preventDefault();let f=y().filter(a=>!a.disabled).map(a=>a.ref.current);ff.includes(a.key)&&f.reverse(),function(a){let b=document.activeElement;for(let c of a)if(c===b||(c.focus(),document.activeElement!==b))return}(f)}),onBlur:p(a.onBlur,a=>{a.currentTarget.contains(a.target)||(window.clearTimeout(E.current),F.current="")}),onPointerMove:p(a.onPointerMove,f8(a=>{let b=a.target,c=K.current!==a.clientX;a.currentTarget.contains(b)&&c&&(J.current=a.clientX>K.current?"right":"left",K.current=a.clientX)}))})})})})})})});fD.displayName=fA;var fI=h.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,g.jsx)(z.sG.div,{role:"group",...d,ref:b})});fI.displayName="MenuGroup";var fJ=h.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,g.jsx)(z.sG.div,{...d,ref:b})});fJ.displayName="MenuLabel";var fK="MenuItem",fL="menu.itemSelect",fM=h.forwardRef((a,b)=>{let{disabled:c=!1,onSelect:d,...e}=a,f=h.useRef(null),i=fu(fK,a.__scopeMenu),j=fC(fK,a.__scopeMenu),k=(0,r.s)(b,f),l=h.useRef(!1);return(0,g.jsx)(fN,{...e,ref:k,disabled:c,onClick:p(a.onClick,()=>{let a=f.current;if(!c&&a){let b=new CustomEvent(fL,{bubbles:!0,cancelable:!0});a.addEventListener(fL,a=>d?.(a),{once:!0}),(0,z.hO)(a,b),b.defaultPrevented?l.current=!1:i.onClose()}}),onPointerDown:b=>{a.onPointerDown?.(b),l.current=!0},onPointerUp:p(a.onPointerUp,a=>{l.current||a.currentTarget?.click()}),onKeyDown:p(a.onKeyDown,a=>{let b=""!==j.searchRef.current;c||b&&" "===a.key||fe.includes(a.key)&&(a.currentTarget.click(),a.preventDefault())})})});fM.displayName=fK;var fN=h.forwardRef((a,b)=>{let{__scopeMenu:c,disabled:d=!1,textValue:e,...f}=a,i=fC(fK,c),j=fq(c),k=h.useRef(null),l=(0,r.s)(b,k),[m,n]=h.useState(!1),[o,q]=h.useState("");return h.useEffect(()=>{let a=k.current;a&&q((a.textContent??"").trim())},[f.children]),(0,g.jsx)(fk.ItemSlot,{scope:c,disabled:d,textValue:e??o,children:(0,g.jsx)(eV,{asChild:!0,...j,focusable:!d,children:(0,g.jsx)(z.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":d||void 0,"data-disabled":d?"":void 0,...f,ref:l,onPointerMove:p(a.onPointerMove,f8(a=>{d?i.onItemLeave(a):(i.onItemEnter(a),a.defaultPrevented||a.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:p(a.onPointerLeave,f8(a=>i.onItemLeave(a))),onFocus:p(a.onFocus,()=>n(!0)),onBlur:p(a.onBlur,()=>n(!1))})})})}),fO=h.forwardRef((a,b)=>{let{checked:c=!1,onCheckedChange:d,...e}=a;return(0,g.jsx)(fW,{scope:a.__scopeMenu,checked:c,children:(0,g.jsx)(fM,{role:"menuitemcheckbox","aria-checked":f6(c)?"mixed":c,...e,ref:b,"data-state":f7(c),onSelect:p(e.onSelect,()=>d?.(!!f6(c)||!c),{checkForDefaultPrevented:!1})})})});fO.displayName="MenuCheckboxItem";var fP="MenuRadioGroup",[fQ,fR]=fn(fP,{value:void 0,onValueChange:()=>{}}),fS=h.forwardRef((a,b)=>{let{value:c,onValueChange:d,...e}=a,f=A(d);return(0,g.jsx)(fQ,{scope:a.__scopeMenu,value:c,onValueChange:f,children:(0,g.jsx)(fI,{...e,ref:b})})});fS.displayName=fP;var fT="MenuRadioItem",fU=h.forwardRef((a,b)=>{let{value:c,...d}=a,e=fR(fT,a.__scopeMenu),f=c===e.value;return(0,g.jsx)(fW,{scope:a.__scopeMenu,checked:f,children:(0,g.jsx)(fM,{role:"menuitemradio","aria-checked":f,...d,ref:b,"data-state":f7(f),onSelect:p(d.onSelect,()=>e.onValueChange?.(c),{checkForDefaultPrevented:!1})})})});fU.displayName=fT;var fV="MenuItemIndicator",[fW,fX]=fn(fV,{checked:!1}),fY=h.forwardRef((a,b)=>{let{__scopeMenu:c,forceMount:d,...e}=a,f=fX(fV,c);return(0,g.jsx)(dt,{present:d||f6(f.checked)||!0===f.checked,children:(0,g.jsx)(z.sG.span,{...e,ref:b,"data-state":f7(f.checked)})})});fY.displayName=fV;var fZ=h.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,g.jsx)(z.sG.div,{role:"separator","aria-orientation":"horizontal",...d,ref:b})});fZ.displayName="MenuSeparator";var f$=h.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=fp(c);return(0,g.jsx)(bx,{...e,...d,ref:b})});f$.displayName="MenuArrow";var[f_,f0]=fn("MenuSub"),f1="MenuSubTrigger",f2=h.forwardRef((a,b)=>{let c=fs(f1,a.__scopeMenu),d=fu(f1,a.__scopeMenu),e=f0(f1,a.__scopeMenu),f=fC(f1,a.__scopeMenu),i=h.useRef(null),{pointerGraceTimerRef:j,onPointerGraceIntentChange:k}=f,l={__scopeMenu:a.__scopeMenu},m=h.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return h.useEffect(()=>m,[m]),h.useEffect(()=>{let a=j.current;return()=>{window.clearTimeout(a),k(null)}},[j,k]),(0,g.jsx)(fv,{asChild:!0,...l,children:(0,g.jsx)(fN,{id:e.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":e.contentId,"data-state":f5(c.open),...a,ref:(0,r.t)(b,e.onTriggerChange),onClick:b=>{a.onClick?.(b),a.disabled||b.defaultPrevented||(b.currentTarget.focus(),c.open||c.onOpenChange(!0))},onPointerMove:p(a.onPointerMove,f8(b=>{f.onItemEnter(b),!b.defaultPrevented&&(a.disabled||c.open||i.current||(f.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{c.onOpenChange(!0),m()},100)))})),onPointerLeave:p(a.onPointerLeave,f8(a=>{m();let b=c.content?.getBoundingClientRect();if(b){let d=c.content?.dataset.side,e="right"===d,g=b[e?"left":"right"],h=b[e?"right":"left"];f.onPointerGraceIntentChange({area:[{x:a.clientX+(e?-5:5),y:a.clientY},{x:g,y:b.top},{x:h,y:b.top},{x:h,y:b.bottom},{x:g,y:b.bottom}],side:d}),window.clearTimeout(j.current),j.current=window.setTimeout(()=>f.onPointerGraceIntentChange(null),300)}else{if(f.onTriggerLeave(a),a.defaultPrevented)return;f.onPointerGraceIntentChange(null)}})),onKeyDown:p(a.onKeyDown,b=>{let e=""!==f.searchRef.current;a.disabled||e&&" "===b.key||fh[d.dir].includes(b.key)&&(c.onOpenChange(!0),c.content?.focus(),b.preventDefault())})})})});f2.displayName=f1;var f3="MenuSubContent",f4=h.forwardRef((a,b)=>{let c=fy(fA,a.__scopeMenu),{forceMount:d=c.forceMount,...e}=a,f=fs(fA,a.__scopeMenu),i=fu(fA,a.__scopeMenu),j=f0(f3,a.__scopeMenu),k=h.useRef(null),l=(0,r.s)(b,k);return(0,g.jsx)(fk.Provider,{scope:a.__scopeMenu,children:(0,g.jsx)(dt,{present:d||f.open,children:(0,g.jsx)(fk.Slot,{scope:a.__scopeMenu,children:(0,g.jsx)(fH,{id:j.contentId,"aria-labelledby":j.triggerId,...e,ref:l,align:"start",side:"rtl"===i.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:a=>{i.isUsingKeyboardRef.current&&k.current?.focus(),a.preventDefault()},onCloseAutoFocus:a=>a.preventDefault(),onFocusOutside:p(a.onFocusOutside,a=>{a.target!==j.trigger&&f.onOpenChange(!1)}),onEscapeKeyDown:p(a.onEscapeKeyDown,a=>{i.onClose(),a.preventDefault()}),onKeyDown:p(a.onKeyDown,a=>{let b=a.currentTarget.contains(a.target),c=fi[i.dir].includes(a.key);b&&c&&(f.onOpenChange(!1),j.trigger?.focus(),a.preventDefault())})})})})})});function f5(a){return a?"open":"closed"}function f6(a){return"indeterminate"===a}function f7(a){return f6(a)?"indeterminate":a?"checked":"unchecked"}function f8(a){return b=>"mouse"===b.pointerType?a(b):void 0}f4.displayName=f3;var f9="DropdownMenu",[ga,gb]=q(f9,[fo]),gc=fo(),[gd,ge]=ga(f9),gf="DropdownMenuTrigger";h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,disabled:d=!1,...e}=a,f=ge(gf,c),h=gc(c);return(0,g.jsx)(fv,{asChild:!0,...h,children:(0,g.jsx)(z.sG.button,{type:"button",id:f.triggerId,"aria-haspopup":"menu","aria-expanded":f.open,"aria-controls":f.open?f.contentId:void 0,"data-state":f.open?"open":"closed","data-disabled":d?"":void 0,disabled:d,...e,ref:(0,r.t)(b,f.triggerRef),onPointerDown:p(a.onPointerDown,a=>{!d&&0===a.button&&!1===a.ctrlKey&&(f.onOpenToggle(),f.open||a.preventDefault())}),onKeyDown:p(a.onKeyDown,a=>{!d&&(["Enter"," "].includes(a.key)&&f.onOpenToggle(),"ArrowDown"===a.key&&f.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})}).displayName=gf;var gg=a=>{let{__scopeDropdownMenu:b,...c}=a,d=gc(b);return(0,g.jsx)(fz,{...d,...c})};gg.displayName="DropdownMenuPortal";var gh="DropdownMenuContent",gi=h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=ge(gh,c),f=gc(c),i=h.useRef(!1);return(0,g.jsx)(fD,{id:e.contentId,"aria-labelledby":e.triggerId,...f,...d,ref:b,onCloseAutoFocus:p(a.onCloseAutoFocus,a=>{i.current||e.triggerRef.current?.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:p(a.onInteractOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey,d=2===b.button||c;(!e.modal||d)&&(i.current=!0)}),style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});gi.displayName=gh,h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=gc(c);return(0,g.jsx)(fI,{...e,...d,ref:b})}).displayName="DropdownMenuGroup";var gj=h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=gc(c);return(0,g.jsx)(fJ,{...e,...d,ref:b})});gj.displayName="DropdownMenuLabel";var gk=h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=gc(c);return(0,g.jsx)(fM,{...e,...d,ref:b})});gk.displayName="DropdownMenuItem";var gl=h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=gc(c);return(0,g.jsx)(fO,{...e,...d,ref:b})});gl.displayName="DropdownMenuCheckboxItem",h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=gc(c);return(0,g.jsx)(fS,{...e,...d,ref:b})}).displayName="DropdownMenuRadioGroup";var gm=h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=gc(c);return(0,g.jsx)(fU,{...e,...d,ref:b})});gm.displayName="DropdownMenuRadioItem";var gn=h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=gc(c);return(0,g.jsx)(fY,{...e,...d,ref:b})});gn.displayName="DropdownMenuItemIndicator";var go=h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=gc(c);return(0,g.jsx)(fZ,{...e,...d,ref:b})});go.displayName="DropdownMenuSeparator",h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=gc(c);return(0,g.jsx)(f$,{...e,...d,ref:b})}).displayName="DropdownMenuArrow";var gp=h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=gc(c);return(0,g.jsx)(f2,{...e,...d,ref:b})});gp.displayName="DropdownMenuSubTrigger";var gq=h.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=gc(c);return(0,g.jsx)(f4,{...e,...d,ref:b,style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});gq.displayName="DropdownMenuSubContent";let gr=(0,di.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),gs=(0,di.A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);h.forwardRef(({className:a,inset:b,children:c,...d},e)=>(0,g.jsxs)(gp,{ref:e,className:(0,dm.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",b&&"pl-8",a),...d,children:[c,(0,g.jsx)(gr,{className:"ml-auto h-4 w-4"})]})).displayName=gp.displayName,h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(gq,{ref:c,className:(0,dm.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...b})).displayName=gq.displayName,h.forwardRef(({className:a,sideOffset:b=4,...c},d)=>(0,g.jsx)(gg,{children:(0,g.jsx)(gi,{ref:d,sideOffset:b,className:(0,dm.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...c})})).displayName=gi.displayName,h.forwardRef(({className:a,inset:b,...c},d)=>(0,g.jsx)(gk,{ref:d,className:(0,dm.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",b&&"pl-8",a),...c})).displayName=gk.displayName,h.forwardRef(({className:a,children:b,checked:c,...d},e)=>(0,g.jsxs)(gl,{ref:e,className:(0,dm.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:c,...d,children:[(0,g.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,g.jsx)(gn,{children:(0,g.jsx)(dl,{className:"h-4 w-4"})})}),b]})).displayName=gl.displayName,h.forwardRef(({className:a,children:b,...c},d)=>(0,g.jsxs)(gm,{ref:d,className:(0,dm.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,g.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,g.jsx)(gn,{children:(0,g.jsx)(gs,{className:"h-2 w-2 fill-current"})})}),b]})).displayName=gm.displayName,h.forwardRef(({className:a,inset:b,...c},d)=>(0,g.jsx)(gj,{ref:d,className:(0,dm.cn)("px-2 py-1.5 text-sm font-semibold",b&&"pl-8",a),...c})).displayName=gj.displayName,h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(go,{ref:c,className:(0,dm.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=go.displayName,c(1669);var gt="Checkbox",[gu,gv]=q(gt),[gw,gx]=gu(gt);function gy(a){let{__scopeCheckbox:b,checked:c,children:d,defaultChecked:e,disabled:f,form:i,name:j,onCheckedChange:k,required:l,value:m="on",internal_do_not_use_render:n}=a,[o,p]=bD({prop:c,defaultProp:e??!1,onChange:k,caller:gt}),[q,r]=h.useState(null),[s,t]=h.useState(null),u=h.useRef(!1),v=!q||!!i||!!q.closest("form"),w={checked:o,disabled:f,setChecked:p,control:q,setControl:r,name:j,form:i,value:m,hasConsumerStoppedPropagationRef:u,required:l,defaultChecked:!gG(e)&&e,isFormControl:v,bubbleInput:s,setBubbleInput:t};return(0,g.jsx)(gw,{scope:b,...w,children:"function"==typeof n?n(w):d})}var gz="CheckboxTrigger",gA=h.forwardRef(({__scopeCheckbox:a,onKeyDown:b,onClick:c,...d},e)=>{let{control:f,value:i,disabled:j,checked:k,required:l,setControl:m,setChecked:n,hasConsumerStoppedPropagationRef:o,isFormControl:q,bubbleInput:s}=gx(gz,a),t=(0,r.s)(e,m),u=h.useRef(k);return h.useEffect(()=>{let a=f?.form;if(a){let b=()=>n(u.current);return a.addEventListener("reset",b),()=>a.removeEventListener("reset",b)}},[f,n]),(0,g.jsx)(z.sG.button,{type:"button",role:"checkbox","aria-checked":gG(k)?"mixed":k,"aria-required":l,"data-state":gH(k),"data-disabled":j?"":void 0,disabled:j,value:i,...d,ref:t,onKeyDown:p(b,a=>{"Enter"===a.key&&a.preventDefault()}),onClick:p(c,a=>{n(a=>!!gG(a)||!a),s&&q&&(o.current=a.isPropagationStopped(),o.current||a.stopPropagation())})})});gA.displayName=gz;var gB=h.forwardRef((a,b)=>{let{__scopeCheckbox:c,name:d,checked:e,defaultChecked:f,required:h,disabled:i,value:j,onCheckedChange:k,form:l,...m}=a;return(0,g.jsx)(gy,{__scopeCheckbox:c,checked:e,defaultChecked:f,disabled:i,required:h,onCheckedChange:k,name:d,form:l,value:j,internal_do_not_use_render:({isFormControl:a})=>(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(gA,{...m,ref:b,__scopeCheckbox:c}),a&&(0,g.jsx)(gF,{__scopeCheckbox:c})]})})});gB.displayName=gt;var gC="CheckboxIndicator",gD=h.forwardRef((a,b)=>{let{__scopeCheckbox:c,forceMount:d,...e}=a,f=gx(gC,c);return(0,g.jsx)(dt,{present:d||gG(f.checked)||!0===f.checked,children:(0,g.jsx)(z.sG.span,{"data-state":gH(f.checked),"data-disabled":f.disabled?"":void 0,...e,ref:b,style:{pointerEvents:"none",...a.style}})})});gD.displayName=gC;var gE="CheckboxBubbleInput",gF=h.forwardRef(({__scopeCheckbox:a,...b},c)=>{let{control:d,hasConsumerStoppedPropagationRef:e,checked:f,defaultChecked:i,required:j,disabled:k,name:l,value:m,form:n,bubbleInput:o,setBubbleInput:p}=gx(gE,a),q=(0,r.s)(c,p),s=bE(f),t=bi(d);h.useEffect(()=>{if(!o)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,b=!e.current;if(s!==f&&a){let c=new Event("click",{bubbles:b});o.indeterminate=gG(f),a.call(o,!gG(f)&&f),o.dispatchEvent(c)}},[o,s,f,e]);let u=h.useRef(!gG(f)&&f);return(0,g.jsx)(z.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:i??u.current,required:j,disabled:k,name:l,value:m,form:n,...b,tabIndex:-1,ref:q,style:{...b.style,...t,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function gG(a){return"indeterminate"===a}function gH(a){return gG(a)?"indeterminate":a?"checked":"unchecked"}gF.displayName=gE;let gI=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(gB,{ref:c,className:(0,dm.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...b,children:(0,g.jsx)(gD,{className:(0,dm.cn)("flex items-center justify-center text-current"),children:(0,g.jsx)(dl,{className:"h-4 w-4"})})}));gI.displayName=gB.displayName;var gJ="Radio",[gK,gL]=q(gJ),[gM,gN]=gK(gJ),gO=h.forwardRef((a,b)=>{let{__scopeRadio:c,name:d,checked:e=!1,required:f,disabled:i,value:j="on",onCheck:k,form:l,...m}=a,[n,o]=h.useState(null),q=(0,r.s)(b,a=>o(a)),s=h.useRef(!1),t=!n||l||!!n.closest("form");return(0,g.jsxs)(gM,{scope:c,checked:e,disabled:i,children:[(0,g.jsx)(z.sG.button,{type:"button",role:"radio","aria-checked":e,"data-state":gS(e),"data-disabled":i?"":void 0,disabled:i,value:j,...m,ref:q,onClick:p(a.onClick,a=>{e||k?.(),t&&(s.current=a.isPropagationStopped(),s.current||a.stopPropagation())})}),t&&(0,g.jsx)(gR,{control:n,bubbles:!s.current,name:d,value:j,checked:e,required:f,disabled:i,form:l,style:{transform:"translateX(-100%)"}})]})});gO.displayName=gJ;var gP="RadioIndicator",gQ=h.forwardRef((a,b)=>{let{__scopeRadio:c,forceMount:d,...e}=a,f=gN(gP,c);return(0,g.jsx)(dt,{present:d||f.checked,children:(0,g.jsx)(z.sG.span,{"data-state":gS(f.checked),"data-disabled":f.disabled?"":void 0,...e,ref:b})})});gQ.displayName=gP;var gR=h.forwardRef(({__scopeRadio:a,control:b,checked:c,bubbles:d=!0,...e},f)=>{let i=h.useRef(null),j=(0,r.s)(i,f),k=bE(c),l=bi(b);return h.useEffect(()=>{let a=i.current;if(!a)return;let b=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(k!==c&&b){let e=new Event("click",{bubbles:d});b.call(a,c),a.dispatchEvent(e)}},[k,c,d]),(0,g.jsx)(z.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:c,...e,tabIndex:-1,ref:j,style:{...e.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function gS(a){return a?"checked":"unchecked"}gR.displayName="RadioBubbleInput";var gT=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],gU="RadioGroup",[gV,gW]=q(gU,[eP,gL]),gX=eP(),gY=gL(),[gZ,g$]=gV(gU),g_=h.forwardRef((a,b)=>{let{__scopeRadioGroup:c,name:d,defaultValue:e,value:f,required:h=!1,disabled:i=!1,orientation:j,dir:k,loop:l=!0,onValueChange:m,...n}=a,o=gX(c),p=y(k),[q,r]=bD({prop:f,defaultProp:e??null,onChange:m,caller:gU});return(0,g.jsx)(gZ,{scope:c,name:d,required:h,disabled:i,value:q,onValueChange:r,children:(0,g.jsx)(eS,{asChild:!0,...o,orientation:j,dir:p,loop:l,children:(0,g.jsx)(z.sG.div,{role:"radiogroup","aria-required":h,"aria-orientation":j,"data-disabled":i?"":void 0,dir:p,...n,ref:b})})})});g_.displayName=gU;var g0="RadioGroupItem",g1=h.forwardRef((a,b)=>{let{__scopeRadioGroup:c,disabled:d,...e}=a,f=g$(g0,c),i=f.disabled||d,j=gX(c),k=gY(c),l=h.useRef(null),m=(0,r.s)(b,l),n=f.value===e.value,o=h.useRef(!1);return h.useEffect(()=>{let a=a=>{gT.includes(a.key)&&(o.current=!0)},b=()=>o.current=!1;return document.addEventListener("keydown",a),document.addEventListener("keyup",b),()=>{document.removeEventListener("keydown",a),document.removeEventListener("keyup",b)}},[]),(0,g.jsx)(eV,{asChild:!0,...j,focusable:!i,active:n,children:(0,g.jsx)(gO,{disabled:i,required:f.required,checked:n,...k,...e,name:f.name,ref:m,onCheck:()=>f.onValueChange(e.value),onKeyDown:p(a=>{"Enter"===a.key&&a.preventDefault()}),onFocus:p(e.onFocus,()=>{o.current&&l.current?.click()})})})});g1.displayName=g0;var g2=h.forwardRef((a,b)=>{let{__scopeRadioGroup:c,...d}=a,e=gY(c);return(0,g.jsx)(gQ,{...e,...d,ref:b})});g2.displayName="RadioGroupIndicator";let g3=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(g_,{className:(0,dm.cn)("grid gap-2",a),...b,ref:c}));g3.displayName=g_.displayName;let g4=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(g1,{ref:c,className:(0,dm.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",a),...b,children:(0,g.jsx)(g2,{className:"flex items-center justify-center",children:(0,g.jsx)(gs,{className:"h-3.5 w-3.5 fill-current text-current"})})}));g4.displayName=g1.displayName;var g5="Switch",[g6,g7]=q(g5),[g8,g9]=g6(g5),ha=h.forwardRef((a,b)=>{let{__scopeSwitch:c,name:d,checked:e,defaultChecked:f,required:i,disabled:j,value:k="on",onCheckedChange:l,form:m,...n}=a,[o,q]=h.useState(null),s=(0,r.s)(b,a=>q(a)),t=h.useRef(!1),u=!o||m||!!o.closest("form"),[v,w]=bD({prop:e,defaultProp:f??!1,onChange:l,caller:g5});return(0,g.jsxs)(g8,{scope:c,checked:v,disabled:j,children:[(0,g.jsx)(z.sG.button,{type:"button",role:"switch","aria-checked":v,"aria-required":i,"data-state":he(v),"data-disabled":j?"":void 0,disabled:j,value:k,...n,ref:s,onClick:p(a.onClick,a=>{w(a=>!a),u&&(t.current=a.isPropagationStopped(),t.current||a.stopPropagation())})}),u&&(0,g.jsx)(hd,{control:o,bubbles:!t.current,name:d,value:k,checked:v,required:i,disabled:j,form:m,style:{transform:"translateX(-100%)"}})]})});ha.displayName=g5;var hb="SwitchThumb",hc=h.forwardRef((a,b)=>{let{__scopeSwitch:c,...d}=a,e=g9(hb,c);return(0,g.jsx)(z.sG.span,{"data-state":he(e.checked),"data-disabled":e.disabled?"":void 0,...d,ref:b})});hc.displayName=hb;var hd=h.forwardRef(({__scopeSwitch:a,control:b,checked:c,bubbles:d=!0,...e},f)=>{let i=h.useRef(null),j=(0,r.s)(i,f),k=bE(c),l=bi(b);return h.useEffect(()=>{let a=i.current;if(!a)return;let b=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(k!==c&&b){let e=new Event("click",{bubbles:d});b.call(a,c),a.dispatchEvent(e)}},[k,c,d]),(0,g.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:c,...e,tabIndex:-1,ref:j,style:{...e.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function he(a){return a?"checked":"unchecked"}hd.displayName="SwitchBubbleInput";let hf=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)(ha,{className:(0,dm.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...b,ref:c,children:(0,g.jsx)(hc,{className:(0,dm.cn)("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})}));hf.displayName=ha.displayName,(0,di.A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);let hg=h.forwardRef(({...a},b)=>(0,g.jsx)("nav",{ref:b,"aria-label":"breadcrumb",...a}));hg.displayName="Breadcrumb";let hh=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("ol",{ref:c,className:(0,dm.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",a),...b}));hh.displayName="BreadcrumbList";let hi=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("li",{ref:c,className:(0,dm.cn)("inline-flex items-center gap-1.5",a),...b}));hi.displayName="BreadcrumbItem";let hj=h.forwardRef(({asChild:a,className:b,...c},d)=>{let e=a?s.DX:"a";return(0,g.jsx)(e,{ref:d,className:(0,dm.cn)("transition-colors hover:text-foreground",b),...c})});hj.displayName="BreadcrumbLink";let hk=h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("span",{ref:c,role:"link","aria-disabled":"true","aria-current":"page",className:(0,dm.cn)("font-normal text-foreground",a),...b}));hk.displayName="BreadcrumbPage";let hl=({children:a,className:b,...c})=>(0,g.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,dm.cn)("[&>svg]:size-3.5",b),...c,children:a??(0,g.jsx)(gr,{})});hl.displayName="BreadcrumbSeparator",(0,di.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("ul",{ref:c,className:(0,dm.cn)("flex flex-row items-center gap-1",a),...b})).displayName="PaginationContent",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("li",{ref:c,className:(0,dm.cn)("",a),...b})).displayName="PaginationItem";let hm=(0,d7.F)("relative z-10 flex max-w-max flex-1 items-center justify-center",{variants:{orientation:{horizontal:"flex-row",vertical:"flex-col"}},defaultVariants:{orientation:"horizontal"}});h.forwardRef(({className:a,orientation:b,children:c,...d},e)=>(0,g.jsx)("nav",{ref:e,className:(0,dm.cn)(hm({orientation:b}),a),...d,children:c})).displayName="NavigationMenu",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("ul",{ref:c,className:(0,dm.cn)("group flex flex-1 list-none items-center justify-center space-x-1",a),...b})).displayName="NavigationMenuList",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("li",{ref:c,className:(0,dm.cn)("",a),...b})).displayName="NavigationMenuItem";let hn=(0,d7.F)("group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50");h.forwardRef(({className:a,children:b,...c},d)=>(0,g.jsxs)("button",{ref:d,className:(0,dm.cn)(hn(),"group",a),...c,children:[b," ",(0,g.jsx)(dj,{className:"relative top-[1px] ml-1 h-3 w-3 transition duration-300 group-data-[state=open]:rotate-180","aria-hidden":"true"})]})).displayName="NavigationMenuTrigger",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("div",{ref:c,className:(0,dm.cn)("left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto",a),...b})).displayName="NavigationMenuContent",h.forwardRef(({className:a,active:b,...c},d)=>(0,g.jsx)("a",{ref:d,className:(0,dm.cn)(hn(),"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",b&&"bg-accent text-accent-foreground",a),...c})).displayName="NavigationMenuLink",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("div",{ref:c,className:(0,dm.cn)("top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in",a),...b,children:(0,g.jsx)("div",{className:"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md"})})).displayName="NavigationMenuIndicator",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("div",{className:(0,dm.cn)("absolute left-0 top-full flex justify-center"),children:(0,g.jsx)("div",{ref:c,className:(0,dm.cn)("relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full origin-top-center overflow-hidden rounded-md border bg-popover text-popover-foreground shadow data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]",a),...b})})).displayName="NavigationMenuViewport",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("div",{className:"relative w-full overflow-auto",children:(0,g.jsx)("table",{ref:c,className:(0,dm.cn)("w-full caption-bottom text-sm",a),...b})})).displayName="Table",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("thead",{ref:c,className:(0,dm.cn)("[&_tr]:border-b",a),...b})).displayName="TableHeader",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("tbody",{ref:c,className:(0,dm.cn)("[&_tr:last-child]:border-0",a),...b})).displayName="TableBody",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("tfoot",{ref:c,className:(0,dm.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...b})).displayName="TableFooter",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("tr",{ref:c,className:(0,dm.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...b})).displayName="TableRow",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("th",{ref:c,className:(0,dm.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...b})).displayName="TableHead",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("td",{ref:c,className:(0,dm.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...b})).displayName="TableCell",h.forwardRef(({className:a,...b},c)=>(0,g.jsx)("caption",{ref:c,className:(0,dm.cn)("mt-4 text-sm text-muted-foreground",a),...b})).displayName="TableCaption";let ho=(0,di.A)("file-x",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]]),hp=(0,di.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),hq=(0,d7.F)("flex flex-col items-center justify-center text-center",{variants:{size:{sm:"py-8 px-4",default:"py-12 px-6",lg:"py-16 px-8"}},defaultVariants:{size:"default"}});function hr({className:a,size:b,icon:c=ho,title:d,description:e,action:f,...h}){return(0,g.jsxs)("div",{className:(0,dm.cn)(hq({size:b}),a),...h,children:[(0,g.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,g.jsx)(c,{className:"h-10 w-10 text-muted-foreground"})}),(0,g.jsxs)("div",{className:"mt-6 space-y-2",children:[(0,g.jsx)("h3",{className:"text-lg font-semibold text-foreground",children:d}),e&&(0,g.jsx)("p",{className:"text-sm text-muted-foreground max-w-sm",children:e})]}),f&&(0,g.jsx)("div",{className:"mt-6",children:(0,g.jsx)(j.$,{onClick:f.onClick,variant:f.variant||"default",children:f.label})})]})}let hs=a=>(0,g.jsx)(hr,{icon:hp,title:"No results found",description:"Try adjusting your search or filter to find what you're looking for.",...a}),ht=(0,di.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);h.Component;var hu=c(4027);let hv=(0,di.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);function hw(){let[a,b]=h.useState(!1);return(0,g.jsx)(eq,{children:(0,g.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,g.jsxs)("div",{className:"space-y-2",children:[(0,g.jsx)("h1",{className:"text-3xl font-bold",children:"UI Components Demo"}),(0,g.jsx)("p",{className:"text-muted-foreground",children:"A showcase of all the UI components in our design system."})]}),(0,g.jsxs)(e2,{defaultValue:"buttons",className:"space-y-4",children:[(0,g.jsxs)(fb,{children:[(0,g.jsx)(fc,{value:"buttons",children:"Buttons & Actions"}),(0,g.jsx)(fc,{value:"forms",children:"Forms"}),(0,g.jsx)(fc,{value:"feedback",children:"Feedback"}),(0,g.jsx)(fc,{value:"navigation",children:"Navigation"}),(0,g.jsx)(fc,{value:"data",children:"Data Display"})]}),(0,g.jsxs)(fd,{value:"buttons",className:"space-y-6",children:[(0,g.jsxs)(m.Zp,{className:"p-6",children:[(0,g.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Buttons"}),(0,g.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,g.jsx)(j.$,{children:"Default"}),(0,g.jsx)(j.$,{variant:"secondary",children:"Secondary"}),(0,g.jsx)(j.$,{variant:"outline",children:"Outline"}),(0,g.jsx)(j.$,{variant:"ghost",children:"Ghost"}),(0,g.jsx)(j.$,{variant:"link",children:"Link"}),(0,g.jsx)(j.$,{variant:"destructive",children:"Destructive"})]})]}),(0,g.jsxs)(m.Zp,{className:"p-6",children:[(0,g.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Button Sizes"}),(0,g.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,g.jsx)(j.$,{size:"sm",children:"Small"}),(0,g.jsx)(j.$,{size:"default",children:"Default"}),(0,g.jsx)(j.$,{size:"lg",children:"Large"}),(0,g.jsx)(j.$,{size:"icon",children:(0,g.jsx)(hu.A,{className:"h-4 w-4"})})]})]}),(0,g.jsxs)(m.Zp,{className:"p-6",children:[(0,g.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Badges"}),(0,g.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,g.jsx)(d6.E,{children:"Default"}),(0,g.jsx)(d6.E,{variant:"secondary",children:"Secondary"}),(0,g.jsx)(d6.E,{variant:"outline",children:"Outline"}),(0,g.jsx)(d6.E,{variant:"destructive",children:"Destructive"}),(0,g.jsx)(d6.E,{variant:"success",children:"Success"}),(0,g.jsx)(d6.E,{variant:"warning",children:"Warning"}),(0,g.jsx)(d6.E,{variant:"info",children:"Info"})]})]})]}),(0,g.jsx)(fd,{value:"forms",className:"space-y-6",children:(0,g.jsxs)(m.Zp,{className:"p-6",children:[(0,g.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Form Controls"}),(0,g.jsxs)("div",{className:"space-y-4 max-w-md",children:[(0,g.jsxs)("div",{className:"space-y-2",children:[(0,g.jsx)(l.J,{htmlFor:"email",children:"Email"}),(0,g.jsx)(k.p,{id:"email",type:"email",placeholder:"Enter your email"})]}),(0,g.jsxs)("div",{className:"space-y-2",children:[(0,g.jsx)(l.J,{htmlFor:"select",children:"Select Option"}),(0,g.jsxs)(cD,{children:[(0,g.jsx)(dn,{children:(0,g.jsx)(cH,{placeholder:"Select an option"})}),(0,g.jsxs)(dr,{children:[(0,g.jsx)(ds,{value:"option1",children:"Option 1"}),(0,g.jsx)(ds,{value:"option2",children:"Option 2"}),(0,g.jsx)(ds,{value:"option3",children:"Option 3"})]})]})]}),(0,g.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,g.jsx)(gI,{id:"terms"}),(0,g.jsx)(l.J,{htmlFor:"terms",children:"Accept terms and conditions"})]}),(0,g.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,g.jsx)(hf,{id:"notifications"}),(0,g.jsx)(l.J,{htmlFor:"notifications",children:"Enable notifications"})]}),(0,g.jsxs)("div",{className:"space-y-2",children:[(0,g.jsx)(l.J,{children:"Choose an option"}),(0,g.jsxs)(g3,{defaultValue:"option1",children:[(0,g.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,g.jsx)(g4,{value:"option1",id:"r1"}),(0,g.jsx)(l.J,{htmlFor:"r1",children:"Option 1"})]}),(0,g.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,g.jsx)(g4,{value:"option2",id:"r2"}),(0,g.jsx)(l.J,{htmlFor:"r2",children:"Option 2"})]})]})]})]})]})}),(0,g.jsxs)(fd,{value:"feedback",className:"space-y-6",children:[(0,g.jsxs)(m.Zp,{className:"p-6",children:[(0,g.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Alerts"}),(0,g.jsxs)("div",{className:"space-y-4",children:[(0,g.jsxs)(eg,{children:[(0,g.jsx)(ec,{className:"h-4 w-4"}),(0,g.jsx)(eh,{children:"Default Alert"}),(0,g.jsx)(ei,{children:"This is a default alert message."})]}),(0,g.jsxs)(eg,{variant:"destructive",children:[(0,g.jsx)(ec,{className:"h-4 w-4"}),(0,g.jsx)(eh,{children:"Error Alert"}),(0,g.jsx)(ei,{children:"Something went wrong. Please try again."})]}),(0,g.jsxs)(eg,{variant:"success",children:[(0,g.jsx)(ec,{className:"h-4 w-4"}),(0,g.jsx)(eh,{children:"Success Alert"}),(0,g.jsx)(ei,{children:"Your action was completed successfully."})]})]})]}),(0,g.jsxs)(m.Zp,{className:"p-6",children:[(0,g.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Loading States"}),(0,g.jsxs)("div",{className:"space-y-4",children:[(0,g.jsxs)("div",{className:"flex items-center gap-4",children:[(0,g.jsx)(d9,{size:"sm"}),(0,g.jsx)(d9,{size:"default"}),(0,g.jsx)(d9,{size:"lg"})]}),(0,g.jsx)(ea,{text:"Loading data..."}),(0,g.jsxs)(j.$,{onClick:()=>{b(!0),setTimeout(()=>b(!1),2e3)},disabled:a,children:[a&&(0,g.jsx)(d9,{size:"sm",className:"mr-2"}),a?"Loading...":"Start Loading"]})]})]}),(0,g.jsxs)(m.Zp,{className:"p-6",children:[(0,g.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Dialog"}),(0,g.jsxs)(dA,{children:[(0,g.jsx)(dC,{asChild:!0,children:(0,g.jsx)(j.$,{children:"Open Dialog"})}),(0,g.jsxs)(d2,{children:[(0,g.jsxs)(d3,{children:[(0,g.jsx)(d4,{children:"Dialog Title"}),(0,g.jsx)(d5,{children:"This is a dialog description. You can put any content here."})]}),(0,g.jsx)("div",{className:"py-4",children:(0,g.jsx)("p",{children:"Dialog content goes here."})})]})]})]})]}),(0,g.jsxs)(fd,{value:"navigation",className:"space-y-6",children:[(0,g.jsxs)(m.Zp,{className:"p-6",children:[(0,g.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Breadcrumb"}),(0,g.jsx)(hg,{children:(0,g.jsxs)(hh,{children:[(0,g.jsx)(hi,{children:(0,g.jsx)(hj,{href:"/",children:(0,g.jsx)(hv,{className:"h-4 w-4"})})}),(0,g.jsx)(hl,{}),(0,g.jsx)(hi,{children:(0,g.jsx)(hj,{href:"/products",children:"Products"})}),(0,g.jsx)(hl,{}),(0,g.jsx)(hi,{children:(0,g.jsx)(hk,{children:"Current Page"})})]})})]}),(0,g.jsxs)(m.Zp,{className:"p-6",children:[(0,g.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Tooltip"}),(0,g.jsxs)(eu,{children:[(0,g.jsx)(ew,{asChild:!0,children:(0,g.jsx)(j.$,{variant:"outline",children:"Hover me"})}),(0,g.jsx)(eH,{children:(0,g.jsx)("p",{children:"This is a tooltip"})})]})]})]}),(0,g.jsx)(fd,{value:"data",className:"space-y-6",children:(0,g.jsxs)(m.Zp,{className:"p-6",children:[(0,g.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Empty States"}),(0,g.jsxs)("div",{className:"space-y-6",children:[(0,g.jsx)(hr,{title:"No data available",description:"There's nothing to show here yet.",action:{label:"Add Data",onClick:()=>alert("Add data clicked")}}),(0,g.jsx)(hs,{})]})]})})]})]})})}},9667:(a,b,c)=>{"use strict";c.d(b,{p:()=>f});var d=c(687);c(3210);var e=c(4780);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[14,365,368,887],()=>b(b.s=6494));module.exports=c})();