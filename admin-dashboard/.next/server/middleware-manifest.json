{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_fb905aa2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tnENcoDIgVcI3R9DZJVPTKSYDn7K5/J2O6AZOfcz3Jw=", "__NEXT_PREVIEW_MODE_ID": "5bc3508577cb7a32321be6489639045d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "63109640cc651aa92ca873701744c9abd27833e0d2da9ce6f9f8e53d770abad0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c6a7f3d43f150cc21a43dc8851f02ccecb13b6857c928d4766eb679c39097310"}}}, "sortedMiddleware": ["/"], "functions": {}}