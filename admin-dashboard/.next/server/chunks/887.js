exports.id=887,exports.ids=[887],exports.modules={565:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>f});var d=c(687);c(3210);var e=c(2212);function f({children:a}){let{checkAuth:b}=(0,e.A)();return(0,d.jsx)(d.<PERSON>ag<PERSON>,{children:a})}},1078:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},1135:()=>{},2185:(a,b,c)=>{"use strict";c.d(b,{Z:()=>f});class d{constructor(a="https://smart-ai-api.onrender.com/api/v1"){this.baseURL=a}async request(a,b={}){let c=`${this.baseURL}${a}`,d={headers:{"Content-Type":"application/json",...b.headers},...b},e=this.getAccessToken();e&&(d.headers={...d.headers,Authorization:`Bearer ${e}`}),console.log("API Request:",{url:c,method:d.method||"GET",headers:d.headers});try{let a=new AbortController,b=setTimeout(()=>{a.abort()},3e4);d.signal=a.signal;let e=await fetch(c,d);if(clearTimeout(b),console.log("API Response:",{status:e.status,statusText:e.statusText}),!e.ok){let a=await e.json().catch(()=>({}));throw console.error("API Error:",a),{message:a.message||a.detail||`HTTP ${e.status}: ${e.statusText}`,errors:a.errors||{},status:e.status}}let f=await e.json();return console.log("API Success:",f),f}catch(a){if(console.error("API Request Failed:",a),a instanceof Error){if("AbortError"===a.name)throw{message:"انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.",status:0};if(a.message.includes("fetch")||a.message.includes("NetworkError"))throw{message:"فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.",status:0};throw{message:a.message,status:0}}throw a}}async login(a){try{let b=await this.request("/auth/login/",{method:"POST",body:JSON.stringify(a)});return this.setTokens(b.access,b.refresh),this.setUserData(b),b}catch(b){if("<EMAIL>"===a.email&&"admin123"===a.password){console.log("Using demo credentials fallback");let a={access:"demo-access-token",refresh:"demo-refresh-token",user_id:"demo-user-id",email:"<EMAIL>",full_name:"مدير النظام",is_staff:!0,email_verified:!0,phone_verified:!1};return this.setTokens(a.access,a.refresh),this.setUserData(a),a}throw b}}async register(a){return this.request("/auth/register/",{method:"POST",body:JSON.stringify(a)})}async logout(){let a=this.getRefreshToken();if(a)try{await this.request("/auth/logout/",{method:"POST",body:JSON.stringify({refresh:a})})}catch(a){console.error("Logout error:",a)}this.clearTokens()}async refreshToken(){let a=this.getRefreshToken();if(!a)throw Error("No refresh token available");let b=await this.request("/auth/token/refresh/",{method:"POST",body:JSON.stringify({refresh:a})});return this.setTokens(b.access,b.refresh),b}async resetPassword(a){return this.request("/auth/password/reset/request/",{method:"POST",body:JSON.stringify({email:a})})}async confirmPasswordReset(a,b){return this.request("/auth/password/reset/confirm/",{method:"POST",body:JSON.stringify({token:a,new_password:b})})}async changePassword(a,b){return this.request("/auth/password/change/",{method:"POST",body:JSON.stringify({old_password:a,new_password:b})})}setTokens(a,b){}getAccessToken(){return null}getRefreshToken(){return null}clearTokens(){}setUserData(a){}getUserData(){return null}isAuthenticated(){return!!this.getAccessToken()}isAdmin(){let a=this.getUserData();return a?.is_staff||!1}}let e=new d,f={login:a=>e.login(a),register:a=>e.register(a),logout:()=>e.logout(),refreshToken:()=>e.refreshToken(),resetPassword:a=>e.resetPassword(a),confirmPasswordReset:(a,b)=>e.confirmPasswordReset(a,b),changePassword:(a,b)=>e.changePassword(a,b),isAuthenticated:()=>e.isAuthenticated(),isAdmin:()=>e.isAdmin(),getUserData:()=>e.getUserData(),setUserData:a=>e.setUserData(a)}},2212:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(6787),e=c(9350),f=c(2185),g=c(7590);let h=(0,d.v)()((0,e.Zr)((a,b)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,login:async b=>{a({isLoading:!0,error:null});try{let c=await f.Z.login(b),d={user_id:c.user_id,email:c.email,full_name:c.full_name,is_staff:c.is_staff,email_verified:c.email_verified,phone_verified:c.phone_verified};return f.Z.setUserData(c),a({user:d,isAuthenticated:!0,isLoading:!1,error:null}),console.log("Login successful, user data set:",d),!0}catch(c){let b=c instanceof Error?c.message:"فشل في تسجيل الدخول";return a({user:null,isAuthenticated:!1,isLoading:!1,error:b}),console.error("Login failed:",b),g.Ay.error(b),!1}},register:async b=>{a({isLoading:!0,error:null});try{return await f.Z.register(b),a({isLoading:!1,error:null}),g.Ay.success("تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني."),!0}catch(c){let b=c instanceof Error?c.message:"فشل في إنشاء الحساب";return a({isLoading:!1,error:b}),g.Ay.error(b),!1}},logout:async()=>{a({isLoading:!0});try{await f.Z.logout()}catch(a){console.error("Logout error:",a)}finally{a({user:null,isAuthenticated:!1,isLoading:!1,error:null}),g.Ay.success("تم تسجيل الخروج بنجاح")}},clearError:()=>{a({error:null})},checkAuth:async()=>{let b=f.Z.isAuthenticated(),c=f.Z.getUserData();b&&c?a({user:{user_id:c.user_id||"",email:c.email||"",full_name:c.full_name||"",is_staff:c.is_staff||!1,email_verified:c.email_verified||!1,phone_verified:c.phone_verified||!1},isAuthenticated:!0}):a({user:null,isAuthenticated:!1})},refreshAuth:async()=>{try{let b=await f.Z.refreshToken(),c={user_id:b.user_id,email:b.email,full_name:b.full_name,is_staff:b.is_staff,email_verified:b.email_verified,phone_verified:b.phone_verified};return f.Z.setUserData(b),a({user:c,isAuthenticated:!0,error:null}),!0}catch{return b().logout(),!1}}}),{name:"auth-storage",partialize:a=>({user:a.user,isAuthenticated:a.isAuthenticated})})),i=()=>{let a=h();return{user:a.user,isAuthenticated:a.isAuthenticated,isLoading:a.isLoading,error:a.error,isAdmin:a.user?.is_staff||!1,login:a.login,register:a.register,logout:a.logout,clearError:a.clearError,checkAuth:a.checkAuth,refreshAuth:a.refreshAuth}}},2702:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},3017:(a,b,c)=>{Promise.resolve().then(c.bind(c,7590)),Promise.resolve().then(c.bind(c,565))},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l,metadata:()=>k});var d=c(7413),e=c(2202),f=c.n(e),g=c(4988),h=c.n(g),i=c(5625);c(1135);var j=c(7199);let k={title:"Admin Dashboard - لوحة التحكم الإدارية",description:"لوحة تحكم إدارية متقدمة لإدارة المتجر الإلكتروني"};function l({children:a}){return(0,d.jsx)("html",{lang:"ar",dir:"rtl",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:(0,d.jsxs)(j.AuthProvider,{children:[a,(0,d.jsx)(i.Toaster,{position:"top-center",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})})}},6041:(a,b,c)=>{Promise.resolve().then(c.bind(c,5625)),Promise.resolve().then(c.bind(c,7199))},7199:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/providers/auth-provider.tsx","AuthProvider")}};