{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/admin/layout.tsx"], "sourcesContent": ["import { Metadata } from 'next';\n\nexport const metadata: Metadata = {\n  title: 'Admin Dashboard',\n  description: 'Admin panel for e-commerce store management',\n};\n\nexport default function AdminLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"flex\">\n        {/* Sidebar will be added in Phase 3 */}\n        <aside className=\"w-64 bg-white shadow-sm\">\n          <div className=\"p-4\">\n            <h2 className=\"text-lg font-semibold text-gray-800\">Admin Panel</h2>\n          </div>\n        </aside>\n        \n        {/* Main content */}\n        <main className=\"flex-1\">\n          <div className=\"p-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,YAAY,EAClC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAM,WAAU;8BACf,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;;;;;;8BAKxD,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}