{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/lib/api.ts"], "sourcesContent": ["// API Configuration and Client\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://smart-ai-api.onrender.com/api/v1';\n\n// Mock mode for development\nconst USE_MOCK_API = process.env.NODE_ENV === 'development' || !process.env.NEXT_PUBLIC_API_BASE_URL;\n\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  access: string;\n  refresh: string;\n  user_id: string;\n  email: string;\n  full_name: string;\n  is_staff: boolean;\n  email_verified: boolean;\n  phone_verified: boolean;\n}\n\nexport interface RegisterRequest {\n  email: string;\n  password: string;\n  password_confirm: string;\n  full_name: string;\n  university_id?: string;\n}\n\nexport interface RegisterResponse {\n  message: string;\n  user_id: string;\n  email_verification_required: boolean;\n  phone_verification_required: boolean;\n}\n\nexport interface ApiError {\n  message: string;\n  errors?: Record<string, string[]>;\n  status: number;\n}\n\n// Mock API responses for development\nconst MOCK_USERS = [\n  {\n    email: '<EMAIL>',\n    password: 'admin123',\n    user_id: 'admin-001',\n    full_name: 'مدير النظام',\n    is_staff: true,\n    email_verified: true,\n    phone_verified: true,\n  },\n  {\n    email: '<EMAIL>',\n    password: 'user123',\n    user_id: 'user-001',\n    full_name: 'مستخدم تجريبي',\n    is_staff: false,\n    email_verified: true,\n    phone_verified: false,\n  }\n];\n\n// Mock API functions\nconst mockApi = {\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    // Simulate network delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n\n    const user = MOCK_USERS.find(u => u.email === credentials.email && u.password === credentials.password);\n\n    if (!user) {\n      throw {\n        message: 'بيانات تسجيل الدخول غير صحيحة',\n        errors: { email: ['البريد الإلكتروني أو كلمة المرور غير صحيحة'] },\n        status: 401,\n      } as ApiError;\n    }\n\n    return {\n      access: 'mock-access-token-' + Date.now(),\n      refresh: 'mock-refresh-token-' + Date.now(),\n      user_id: user.user_id,\n      email: user.email,\n      full_name: user.full_name,\n      is_staff: user.is_staff,\n      email_verified: user.email_verified,\n      phone_verified: user.phone_verified,\n    };\n  },\n\n  async register(userData: RegisterRequest): Promise<RegisterResponse> {\n    // Simulate network delay\n    await new Promise(resolve => setTimeout(resolve, 800));\n\n    // Check if user already exists\n    const existingUser = MOCK_USERS.find(u => u.email === userData.email);\n    if (existingUser) {\n      throw {\n        message: 'المستخدم موجود بالفعل',\n        errors: { email: ['هذا البريد الإلكتروني مستخدم بالفعل'] },\n        status: 400,\n      } as ApiError;\n    }\n\n    // Check password confirmation\n    if (userData.password !== userData.password_confirm) {\n      throw {\n        message: 'كلمات المرور غير متطابقة',\n        errors: { password_confirm: ['كلمات المرور غير متطابقة'] },\n        status: 400,\n      } as ApiError;\n    }\n\n    return {\n      message: 'تم إنشاء الحساب بنجاح',\n      user_id: 'user-' + Date.now(),\n      email_verification_required: true,\n      phone_verification_required: true,\n    };\n  },\n\n  async refreshToken(refreshToken: string): Promise<{ access: string }> {\n    // Simulate network delay\n    await new Promise(resolve => setTimeout(resolve, 300));\n\n    if (!refreshToken || !refreshToken.startsWith('mock-refresh-token')) {\n      throw {\n        message: 'رمز التحديث غير صالح',\n        errors: {},\n        status: 401,\n      } as ApiError;\n    }\n\n    return {\n      access: 'mock-access-token-' + Date.now(),\n    };\n  }\n};\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseURL}${endpoint}`;\n\n    const config: RequestInit = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    // Add auth token if available\n    const token = this.getAccessToken();\n    if (token) {\n      config.headers = {\n        ...config.headers,\n        Authorization: `Bearer ${token}`,\n      };\n    }\n\n    console.log('API Request:', { url, method: config.method || 'GET', headers: config.headers });\n\n    try {\n      // Create AbortController for timeout\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => {\n        controller.abort();\n      }, 30000); // 30 seconds timeout\n\n      // Add signal to config\n      config.signal = controller.signal;\n\n      const response = await fetch(url, config);\n      clearTimeout(timeoutId);\n\n      console.log('API Response:', { status: response.status, statusText: response.statusText });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        console.error('API Error:', errorData);\n        throw {\n          message: errorData.message || errorData.detail || `HTTP ${response.status}: ${response.statusText}`,\n          errors: errorData.errors || {},\n          status: response.status,\n        } as ApiError;\n      }\n\n      const data = await response.json();\n      console.log('API Success:', data);\n      return data;\n    } catch (error) {\n      console.error('API Request Failed:', error);\n\n      if (error instanceof Error) {\n        // Handle different types of errors\n        if (error.name === 'AbortError') {\n          throw {\n            message: 'انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.',\n            status: 0,\n          } as ApiError;\n        }\n\n        if (error.message.includes('fetch') || error.message.includes('NetworkError')) {\n          throw {\n            message: 'فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.',\n            status: 0,\n          } as ApiError;\n        }\n\n        throw {\n          message: error.message,\n          status: 0,\n        } as ApiError;\n      }\n      throw error;\n    }\n  }\n\n  // Authentication methods\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    // Use mock API in development mode or when API is not available\n    if (USE_MOCK_API) {\n      console.log('Using mock API for login');\n      try {\n        const response = await mockApi.login(credentials);\n\n        // Store tokens\n        this.setTokens(response.access, response.refresh);\n        this.setUserData(response);\n\n        return response;\n      } catch (error) {\n        console.error('Mock API login error:', error);\n        throw error;\n      }\n    }\n\n    try {\n      const response = await this.request<LoginResponse>('/auth/login/', {\n        method: 'POST',\n        body: JSON.stringify(credentials),\n      });\n\n      // Store tokens\n      this.setTokens(response.access, response.refresh);\n      this.setUserData(response);\n\n      return response;\n    } catch (error) {\n      // Fallback to mock API if real API fails\n      console.log('Real API failed, falling back to mock API');\n      try {\n        const response = await mockApi.login(credentials);\n\n        // Store tokens\n        this.setTokens(response.access, response.refresh);\n        this.setUserData(response);\n\n        return response;\n      } catch (mockError) {\n        console.error('Mock API also failed:', mockError);\n        throw mockError;\n      }\n    }\n  }\n\n  async register(userData: RegisterRequest): Promise<RegisterResponse> {\n    // Use mock API in development mode or when API is not available\n    if (USE_MOCK_API) {\n      console.log('Using mock API for register');\n      return mockApi.register(userData);\n    }\n\n    try {\n      return this.request<RegisterResponse>('/auth/register/', {\n        method: 'POST',\n        body: JSON.stringify(userData),\n      });\n    } catch (error) {\n      // Fallback to mock API if real API fails\n      console.log('Real API failed, falling back to mock API for register');\n      return mockApi.register(userData);\n    }\n  }\n\n  async logout(): Promise<void> {\n    const refreshToken = this.getRefreshToken();\n    if (refreshToken) {\n      try {\n        await this.request('/auth/logout/', {\n          method: 'POST',\n          body: JSON.stringify({ refresh: refreshToken }),\n        });\n      } catch (error) {\n        console.error('Logout error:', error);\n      }\n    }\n    \n    this.clearTokens();\n  }\n\n  async refreshToken(): Promise<LoginResponse> {\n    const refreshToken = this.getRefreshToken();\n    if (!refreshToken) {\n      throw new Error('No refresh token available');\n    }\n\n    // Use mock API in development mode or when API is not available\n    if (USE_MOCK_API) {\n      console.log('Using mock API for token refresh');\n      const response = await mockApi.refreshToken(refreshToken);\n      this.setTokens(response.access, refreshToken); // Keep the same refresh token\n\n      // Return a full LoginResponse (mock doesn't return user data, so we'll use stored data)\n      const userData = this.getUserData();\n      return {\n        access: response.access,\n        refresh: refreshToken,\n        user_id: userData?.user_id || 'mock-user',\n        email: userData?.email || '<EMAIL>',\n        full_name: userData?.full_name || 'مدير النظام',\n        is_staff: userData?.is_staff || true,\n        email_verified: userData?.email_verified || true,\n        phone_verified: userData?.phone_verified || false,\n      };\n    }\n\n    try {\n      const response = await this.request<LoginResponse>('/auth/token/refresh/', {\n        method: 'POST',\n        body: JSON.stringify({ refresh: refreshToken }),\n      });\n\n      this.setTokens(response.access, response.refresh);\n      return response;\n    } catch (error) {\n      // Fallback to mock API if real API fails\n      console.log('Real API failed, falling back to mock API for token refresh');\n      const response = await mockApi.refreshToken(refreshToken);\n      this.setTokens(response.access, refreshToken);\n\n      const userData = this.getUserData();\n      return {\n        access: response.access,\n        refresh: refreshToken,\n        user_id: userData?.user_id || 'mock-user',\n        email: userData?.email || '<EMAIL>',\n        full_name: userData?.full_name || 'مدير النظام',\n        is_staff: userData?.is_staff || true,\n        email_verified: userData?.email_verified || true,\n        phone_verified: userData?.phone_verified || false,\n      };\n    }\n  }\n\n  async resetPassword(email: string): Promise<{ message: string }> {\n    return this.request('/auth/password/reset/request/', {\n      method: 'POST',\n      body: JSON.stringify({ email }),\n    });\n  }\n\n  async confirmPasswordReset(token: string, newPassword: string): Promise<{ message: string }> {\n    return this.request('/auth/password/reset/confirm/', {\n      method: 'POST',\n      body: JSON.stringify({ token, new_password: newPassword }),\n    });\n  }\n\n  async changePassword(oldPassword: string, newPassword: string): Promise<{ message: string }> {\n    return this.request('/auth/password/change/', {\n      method: 'POST',\n      body: JSON.stringify({ old_password: oldPassword, new_password: newPassword }),\n    });\n  }\n\n  // Token management\n  private setTokens(accessToken: string, refreshToken: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('access_token', accessToken);\n      localStorage.setItem('refresh_token', refreshToken);\n    }\n  }\n\n  private getAccessToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('access_token');\n    }\n    return null;\n  }\n\n  private getRefreshToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('refresh_token');\n    }\n    return null;\n  }\n\n  private clearTokens(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('refresh_token');\n      localStorage.removeItem('user_data');\n    }\n  }\n\n  // User data management\n  setUserData(userData: Partial<LoginResponse>): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('user_data', JSON.stringify(userData));\n    }\n  }\n\n  getUserData(): Partial<LoginResponse> | null {\n    if (typeof window !== 'undefined') {\n      const userData = localStorage.getItem('user_data');\n      return userData ? JSON.parse(userData) : null;\n    }\n    return null;\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.getAccessToken();\n  }\n\n  isAdmin(): boolean {\n    const userData = this.getUserData();\n    return userData?.is_staff || false;\n  }\n}\n\n// Export singleton instance\nexport const apiClient = new ApiClient();\n\n// Export individual auth functions for convenience\nexport const authApi = {\n  login: (credentials: LoginRequest) => apiClient.login(credentials),\n  register: (userData: RegisterRequest) => apiClient.register(userData),\n  logout: () => apiClient.logout(),\n  refreshToken: () => apiClient.refreshToken(),\n  resetPassword: (email: string) => apiClient.resetPassword(email),\n  confirmPasswordReset: (token: string, newPassword: string) => \n    apiClient.confirmPasswordReset(token, newPassword),\n  changePassword: (oldPassword: string, newPassword: string) => \n    apiClient.changePassword(oldPassword, newPassword),\n  isAuthenticated: () => apiClient.isAuthenticated(),\n  isAdmin: () => apiClient.isAdmin(),\n  getUserData: () => apiClient.getUserData(),\n  setUserData: (userData: Partial<LoginResponse>) => apiClient.setUserData(userData),\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;AAC/B,MAAM,eAAe,wCAAwC;AAE7D,4BAA4B;AAC5B,MAAM,eAAe,oDAAyB,iBAAiB;AAuC/D,qCAAqC;AACrC,MAAM,aAAa;IACjB;QACE,OAAO;QACP,UAAU;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,gBAAgB;QAChB,gBAAgB;IAClB;IACA;QACE,OAAO;QACP,UAAU;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,gBAAgB;QAChB,gBAAgB;IAClB;CACD;AAED,qBAAqB;AACrB,MAAM,UAAU;IACd,MAAM,OAAM,WAAyB;QACnC,yBAAyB;QACzB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,YAAY,KAAK,IAAI,EAAE,QAAQ,KAAK,YAAY,QAAQ;QAEtG,IAAI,CAAC,MAAM;YACT,MAAM;gBACJ,SAAS;gBACT,QAAQ;oBAAE,OAAO;wBAAC;qBAA6C;gBAAC;gBAChE,QAAQ;YACV;QACF;QAEA,OAAO;YACL,QAAQ,uBAAuB,KAAK,GAAG;YACvC,SAAS,wBAAwB,KAAK,GAAG;YACzC,SAAS,KAAK,OAAO;YACrB,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,SAAS;YACzB,UAAU,KAAK,QAAQ;YACvB,gBAAgB,KAAK,cAAc;YACnC,gBAAgB,KAAK,cAAc;QACrC;IACF;IAEA,MAAM,UAAS,QAAyB;QACtC,yBAAyB;QACzB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,+BAA+B;QAC/B,MAAM,eAAe,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,KAAK;QACpE,IAAI,cAAc;YAChB,MAAM;gBACJ,SAAS;gBACT,QAAQ;oBAAE,OAAO;wBAAC;qBAAsC;gBAAC;gBACzD,QAAQ;YACV;QACF;QAEA,8BAA8B;QAC9B,IAAI,SAAS,QAAQ,KAAK,SAAS,gBAAgB,EAAE;YACnD,MAAM;gBACJ,SAAS;gBACT,QAAQ;oBAAE,kBAAkB;wBAAC;qBAA2B;gBAAC;gBACzD,QAAQ;YACV;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS,UAAU,KAAK,GAAG;YAC3B,6BAA6B;YAC7B,6BAA6B;QAC/B;IACF;IAEA,MAAM,cAAa,YAAoB;QACrC,yBAAyB;QACzB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,IAAI,CAAC,gBAAgB,CAAC,aAAa,UAAU,CAAC,uBAAuB;YACnE,MAAM;gBACJ,SAAS;gBACT,QAAQ,CAAC;gBACT,QAAQ;YACV;QACF;QAEA,OAAO;YACL,QAAQ,uBAAuB,KAAK,GAAG;QACzC;IACF;AACF;AAEA,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,8BAA8B;QAC9B,MAAM,QAAQ,IAAI,CAAC,cAAc;QACjC,IAAI,OAAO;YACT,OAAO,OAAO,GAAG;gBACf,GAAG,OAAO,OAAO;gBACjB,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;QACF;QAEA,QAAQ,GAAG,CAAC,gBAAgB;YAAE;YAAK,QAAQ,OAAO,MAAM,IAAI;YAAO,SAAS,OAAO,OAAO;QAAC;QAE3F,IAAI;YACF,qCAAqC;YACrC,MAAM,aAAa,IAAI;YACvB,MAAM,YAAY,WAAW;gBAC3B,WAAW,KAAK;YAClB,GAAG,QAAQ,qBAAqB;YAEhC,uBAAuB;YACvB,OAAO,MAAM,GAAG,WAAW,MAAM;YAEjC,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,aAAa;YAEb,QAAQ,GAAG,CAAC,iBAAiB;gBAAE,QAAQ,SAAS,MAAM;gBAAE,YAAY,SAAS,UAAU;YAAC;YAExF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,QAAQ,KAAK,CAAC,cAAc;gBAC5B,MAAM;oBACJ,SAAS,UAAU,OAAO,IAAI,UAAU,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;oBACnG,QAAQ,UAAU,MAAM,IAAI,CAAC;oBAC7B,QAAQ,SAAS,MAAM;gBACzB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YAErC,IAAI,iBAAiB,OAAO;gBAC1B,mCAAmC;gBACnC,IAAI,MAAM,IAAI,KAAK,cAAc;oBAC/B,MAAM;wBACJ,SAAS;wBACT,QAAQ;oBACV;gBACF;gBAEA,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY,MAAM,OAAO,CAAC,QAAQ,CAAC,iBAAiB;oBAC7E,MAAM;wBACJ,SAAS;wBACT,QAAQ;oBACV;gBACF;gBAEA,MAAM;oBACJ,SAAS,MAAM,OAAO;oBACtB,QAAQ;gBACV;YACF;YACA,MAAM;QACR;IACF;IAEA,yBAAyB;IACzB,MAAM,MAAM,WAAyB,EAA0B;QAC7D,gEAAgE;QAChE,wCAAkB;YAChB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM,WAAW,MAAM,QAAQ,KAAK,CAAC;gBAErC,eAAe;gBACf,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,EAAE,SAAS,OAAO;gBAChD,IAAI,CAAC,WAAW,CAAC;gBAEjB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,MAAM;YACR;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAgB,gBAAgB;gBACjE,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,eAAe;YACf,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,EAAE,SAAS,OAAO;YAChD,IAAI,CAAC,WAAW,CAAC;YAEjB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,yCAAyC;YACzC,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM,WAAW,MAAM,QAAQ,KAAK,CAAC;gBAErC,eAAe;gBACf,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,EAAE,SAAS,OAAO;gBAChD,IAAI,CAAC,WAAW,CAAC;gBAEjB,OAAO;YACT,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,MAAM;YACR;QACF;IACF;IAEA,MAAM,SAAS,QAAyB,EAA6B;QACnE,gEAAgE;QAChE,wCAAkB;YAChB,QAAQ,GAAG,CAAC;YACZ,OAAO,QAAQ,QAAQ,CAAC;QAC1B;;;IAYF;IAEA,MAAM,SAAwB;QAC5B,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,IAAI,cAAc;YAChB,IAAI;gBACF,MAAM,IAAI,CAAC,OAAO,CAAC,iBAAiB;oBAClC,QAAQ;oBACR,MAAM,KAAK,SAAS,CAAC;wBAAE,SAAS;oBAAa;gBAC/C;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC;QACF;QAEA,IAAI,CAAC,WAAW;IAClB;IAEA,MAAM,eAAuC;QAC3C,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,gEAAgE;QAChE,wCAAkB;YAChB,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,QAAQ,YAAY,CAAC;YAC5C,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,EAAE,eAAe,8BAA8B;YAE7E,wFAAwF;YACxF,MAAM,WAAW,IAAI,CAAC,WAAW;YACjC,OAAO;gBACL,QAAQ,SAAS,MAAM;gBACvB,SAAS;gBACT,SAAS,UAAU,WAAW;gBAC9B,OAAO,UAAU,SAAS;gBAC1B,WAAW,UAAU,aAAa;gBAClC,UAAU,UAAU,YAAY;gBAChC,gBAAgB,UAAU,kBAAkB;gBAC5C,gBAAgB,UAAU,kBAAkB;YAC9C;QACF;;;IA4BF;IAEA,MAAM,cAAc,KAAa,EAAgC;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAC,iCAAiC;YACnD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;IACF;IAEA,MAAM,qBAAqB,KAAa,EAAE,WAAmB,EAAgC;QAC3F,OAAO,IAAI,CAAC,OAAO,CAAC,iCAAiC;YACnD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO,cAAc;YAAY;QAC1D;IACF;IAEA,MAAM,eAAe,WAAmB,EAAE,WAAmB,EAAgC;QAC3F,OAAO,IAAI,CAAC,OAAO,CAAC,0BAA0B;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,cAAc;gBAAa,cAAc;YAAY;QAC9E;IACF;IAEA,mBAAmB;IACX,UAAU,WAAmB,EAAE,YAAoB,EAAQ;QACjE;;IAIF;IAEQ,iBAAgC;QACtC;;QAGA,OAAO;IACT;IAEQ,kBAAiC;QACvC;;QAGA,OAAO;IACT;IAEQ,cAAoB;QAC1B;;IAKF;IAEA,uBAAuB;IACvB,YAAY,QAAgC,EAAQ;QAClD;;IAGF;IAEA,cAA6C;QAC3C;;QAIA,OAAO;IACT;IAEA,kBAA2B;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc;IAC9B;IAEA,UAAmB;QACjB,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,OAAO,UAAU,YAAY;IAC/B;AACF;AAGO,MAAM,YAAY,IAAI;AAGtB,MAAM,UAAU;IACrB,OAAO,CAAC,cAA8B,UAAU,KAAK,CAAC;IACtD,UAAU,CAAC,WAA8B,UAAU,QAAQ,CAAC;IAC5D,QAAQ,IAAM,UAAU,MAAM;IAC9B,cAAc,IAAM,UAAU,YAAY;IAC1C,eAAe,CAAC,QAAkB,UAAU,aAAa,CAAC;IAC1D,sBAAsB,CAAC,OAAe,cACpC,UAAU,oBAAoB,CAAC,OAAO;IACxC,gBAAgB,CAAC,aAAqB,cACpC,UAAU,cAAc,CAAC,aAAa;IACxC,iBAAiB,IAAM,UAAU,eAAe;IAChD,SAAS,IAAM,UAAU,OAAO;IAChC,aAAa,IAAM,UAAU,WAAW;IACxC,aAAa,CAAC,WAAqC,UAAU,WAAW,CAAC;AAC3E", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/stores/auth.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { authApi, LoginRequest, RegisterRequest } from '@/lib/api';\nimport toast from 'react-hot-toast';\n\ninterface User {\n  user_id: string;\n  email: string;\n  full_name: string;\n  is_staff: boolean;\n  email_verified: boolean;\n  phone_verified: boolean;\n}\n\ninterface AuthState {\n  // State\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n\n  // Actions\n  login: (credentials: LoginRequest) => Promise<boolean>;\n  register: (userData: RegisterRequest) => Promise<boolean>;\n  logout: () => Promise<void>;\n  clearError: () => void;\n  checkAuth: () => Promise<void>;\n  refreshAuth: () => Promise<boolean>;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // Login action\n      login: async (credentials: LoginRequest): Promise<boolean> => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const response = await authApi.login(credentials);\n          \n          const user: User = {\n            user_id: response.user_id,\n            email: response.email,\n            full_name: response.full_name,\n            is_staff: response.is_staff,\n            email_verified: response.email_verified,\n            phone_verified: response.phone_verified,\n          };\n\n          // Store user data in API client\n          authApi.setUserData(response);\n\n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n\n          console.log('Login successful, user data set:', user);\n          console.log('isAuthenticated set to:', true);\n          // إزالة رسالة الترحيب لتجنب ظهورها\n          // toast.success(`مرحباً ${user.full_name}!`);\n          \n          // إزالة التأخير لجعل العملية أسرع\n          // await new Promise(resolve => setTimeout(resolve, 100));\n          \n          return true;\n        } catch (error: unknown) {\n          const errorMessage = error instanceof Error ? error.message : 'فشل في تسجيل الدخول';\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          console.error('Login failed:', errorMessage);\n          toast.error(errorMessage);\n          return false;\n        }\n      },\n\n      // Register action\n      register: async (userData: RegisterRequest): Promise<boolean> => {\n        set({ isLoading: true, error: null });\n\n        try {\n          await authApi.register(userData);\n          \n          set({\n            isLoading: false,\n            error: null,\n          });\n\n          toast.success('تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني.');\n          return true;\n        } catch (error: unknown) {\n          const errorMessage = error instanceof Error ? error.message : 'فشل في إنشاء الحساب';\n          set({\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          toast.error(errorMessage);\n          return false;\n        }\n      },\n\n      // Logout action\n      logout: async (): Promise<void> => {\n        set({ isLoading: true });\n\n        try {\n          await authApi.logout();\n        } catch (error) {\n          console.error('Logout error:', error);\n        } finally {\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n\n          toast.success('تم تسجيل الخروج بنجاح');\n        }\n      },\n\n      // Clear error\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // Check authentication status\n      checkAuth: async (): Promise<void> => {\n        const isAuth = authApi.isAuthenticated();\n        const userData = authApi.getUserData();\n\n        if (isAuth && userData) {\n          const user: User = {\n            user_id: userData.user_id || '',\n            email: userData.email || '',\n            full_name: userData.full_name || '',\n            is_staff: userData.is_staff || false,\n            email_verified: userData.email_verified || false,\n            phone_verified: userData.phone_verified || false,\n          };\n\n          set({\n            user,\n            isAuthenticated: true,\n          });\n        } else {\n          set({\n            user: null,\n            isAuthenticated: false,\n          });\n        }\n      },\n\n      // Refresh authentication\n      refreshAuth: async (): Promise<boolean> => {\n        try {\n          const response = await authApi.refreshToken();\n          \n          const user: User = {\n            user_id: response.user_id,\n            email: response.email,\n            full_name: response.full_name,\n            is_staff: response.is_staff,\n            email_verified: response.email_verified,\n            phone_verified: response.phone_verified,\n          };\n\n          authApi.setUserData(response);\n\n          set({\n            user,\n            isAuthenticated: true,\n            error: null,\n          });\n\n          return true;\n        } catch {\n          // If refresh fails, logout user\n          get().logout();\n          return false;\n        }\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// Helper hooks\nexport const useAuth = () => {\n  const store = useAuthStore();\n  return {\n    user: store.user,\n    isAuthenticated: store.isAuthenticated,\n    isLoading: store.isLoading,\n    error: store.error,\n    isAdmin: store.user?.is_staff || false,\n    login: store.login,\n    register: store.register,\n    logout: store.logout,\n    clearError: store.clearError,\n    checkAuth: store.checkAuth,\n    refreshAuth: store.refreshAuth,\n  };\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AA2BO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,eAAe;QACf,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAErC,MAAM,OAAa;oBACjB,SAAS,SAAS,OAAO;oBACzB,OAAO,SAAS,KAAK;oBACrB,WAAW,SAAS,SAAS;oBAC7B,UAAU,SAAS,QAAQ;oBAC3B,gBAAgB,SAAS,cAAc;oBACvC,gBAAgB,SAAS,cAAc;gBACzC;gBAEA,gCAAgC;gBAChC,iHAAA,CAAA,UAAO,CAAC,WAAW,CAAC;gBAEpB,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,QAAQ,GAAG,CAAC,oCAAoC;gBAChD,QAAQ,GAAG,CAAC,2BAA2B;gBACvC,mCAAmC;gBACnC,8CAA8C;gBAE9C,kCAAkC;gBAClC,0DAA0D;gBAE1D,OAAO;YACT,EAAE,OAAO,OAAgB;gBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,kBAAkB;QAClB,UAAU,OAAO;YACf,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,iHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;gBAEvB,IAAI;oBACF,WAAW;oBACX,OAAO;gBACT;gBAEA,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,OAAgB;gBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBACF,WAAW;oBACX,OAAO;gBACT;gBAEA,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,gBAAgB;QAChB,QAAQ;YACN,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,MAAM,iHAAA,CAAA,UAAO,CAAC,MAAM;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC,SAAU;gBACR,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,cAAc;QACd,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,8BAA8B;QAC9B,WAAW;YACT,MAAM,SAAS,iHAAA,CAAA,UAAO,CAAC,eAAe;YACtC,MAAM,WAAW,iHAAA,CAAA,UAAO,CAAC,WAAW;YAEpC,IAAI,UAAU,UAAU;gBACtB,MAAM,OAAa;oBACjB,SAAS,SAAS,OAAO,IAAI;oBAC7B,OAAO,SAAS,KAAK,IAAI;oBACzB,WAAW,SAAS,SAAS,IAAI;oBACjC,UAAU,SAAS,QAAQ,IAAI;oBAC/B,gBAAgB,SAAS,cAAc,IAAI;oBAC3C,gBAAgB,SAAS,cAAc,IAAI;gBAC7C;gBAEA,IAAI;oBACF;oBACA,iBAAiB;gBACnB;YACF,OAAO;gBACL,IAAI;oBACF,MAAM;oBACN,iBAAiB;gBACnB;YACF;QACF;QAEA,yBAAyB;QACzB,aAAa;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,YAAY;gBAE3C,MAAM,OAAa;oBACjB,SAAS,SAAS,OAAO;oBACzB,OAAO,SAAS,KAAK;oBACrB,WAAW,SAAS,SAAS;oBAC7B,UAAU,SAAS,QAAQ;oBAC3B,gBAAgB,SAAS,cAAc;oBACvC,gBAAgB,SAAS,cAAc;gBACzC;gBAEA,iHAAA,CAAA,UAAO,CAAC,WAAW,CAAC;gBAEpB,IAAI;oBACF;oBACA,iBAAiB;oBACjB,OAAO;gBACT;gBAEA,OAAO;YACT,EAAE,OAAM;gBACN,gCAAgC;gBAChC,MAAM,MAAM;gBACZ,OAAO;YACT;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AAKG,MAAM,UAAU;IACrB,MAAM,QAAQ;IACd,OAAO;QACL,MAAM,MAAM,IAAI;QAChB,iBAAiB,MAAM,eAAe;QACtC,WAAW,MAAM,SAAS;QAC1B,OAAO,MAAM,KAAK;QAClB,SAAS,MAAM,IAAI,EAAE,YAAY;QACjC,OAAO,MAAM,KAAK;QAClB,UAAU,MAAM,QAAQ;QACxB,QAAQ,MAAM,MAAM;QACpB,YAAY,MAAM,UAAU;QAC5B,WAAW,MAAM,SAAS;QAC1B,aAAa,MAAM,WAAW;IAChC;AACF", "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/components/providers/auth-provider.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useAuth } from '@/stores/auth';\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const { checkAuth } = useAuth();\n\n  useEffect(() => {\n    // Check authentication status on app load\n    checkAuth();\n  }, [checkAuth]);\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,UAAO,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0CAA0C;QAC1C;IACF,GAAG;QAAC;KAAU;IAEd,qBAAO;kBAAG;;AACZ", "debugId": null}}]}