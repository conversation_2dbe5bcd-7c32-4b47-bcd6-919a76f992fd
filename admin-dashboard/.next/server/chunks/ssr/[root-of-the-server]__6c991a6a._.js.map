{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/StoreAdminPanel/admin-dashboard/src/app/admin/dashboard/page.tsx"], "sourcesContent": ["export default function DashboardPage() {\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">Dashboard</h1>\n        <p className=\"text-gray-600\">Welcome to your admin dashboard</p>\n      </div>\n      \n      {/* Placeholder for dashboard content */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n          <h3 className=\"text-sm font-medium text-gray-500\">Total Sales</h3>\n          <p className=\"text-2xl font-bold text-gray-900\">$12,345</p>\n        </div>\n        \n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n          <h3 className=\"text-sm font-medium text-gray-500\">Orders</h3>\n          <p className=\"text-2xl font-bold text-gray-900\">123</p>\n        </div>\n        \n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n          <h3 className=\"text-sm font-medium text-gray-500\">Customers</h3>\n          <p className=\"text-2xl font-bold text-gray-900\">456</p>\n        </div>\n        \n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n          <h3 className=\"text-sm font-medium text-gray-500\">Products</h3>\n          <p className=\"text-2xl font-bold text-gray-900\">789</p>\n        </div>\n      </div>\n      \n      <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n          Recent Activity\n        </h2>\n        <p className=\"text-gray-600\">\n          Dashboard content will be implemented in Phase 5\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAGlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAGlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAGlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;0BAIpD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAMrC", "debugId": null}}]}