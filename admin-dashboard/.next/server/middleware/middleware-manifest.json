{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_fb905aa2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|public).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MrOQwvNTpTN8U/K9D5QPd5rtSh5WNbcc6ZOwQF/zcp0=", "__NEXT_PREVIEW_MODE_ID": "219d2696eb0b91f6cc5d2d3927981eb8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a217dc1ad61d7ab7231d0d84873b608c18932380ae68c6f2a9ed3aeee050b5a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6a508a9578d92ccbff4e958e6ccd4c95bc0cf46b35a3a400214b9f73aaabf64f"}}}, "instrumentation": null, "functions": {}}