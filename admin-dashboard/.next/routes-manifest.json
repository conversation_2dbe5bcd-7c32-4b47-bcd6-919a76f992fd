{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}, {"source": "/admin", "destination": "/admin/dashboard", "permanent": true, "regex": "^(?!\\/_next)\\/admin(?:\\/)?$"}], "headers": [{"source": "/admin/:path*", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}], "regex": "^\\/admin(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$"}]}