# Database
DATABASE_URL="postgresql://username:password@localhost:5432/admin_dashboard"

# Authentication
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# API Keys
API_BASE_URL="https://smart-ai-api.onrender.com/api/v1"
NEXT_PUBLIC_API_BASE_URL="https://smart-ai-api.onrender.com/api/v1"

# Admin Panel Configuration
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"

# File Upload
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE="5242880" # 5MB

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Redis (for caching)
REDIS_URL="redis://localhost:6379"

# Environment
NODE_ENV="development"
