{"version": 3, "sources": ["../src/index.ts", "../src/roving-focus-group.tsx"], "sourcesContent": ["'use client';\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n} from './roving-focus-group';\nexport type { RovingFocusGroupProps, RovingFocusItemProps } from './roving-focus-group';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,YAAuB;AACvB,uBAAqC;AACrC,8BAAiC;AACjC,gCAAgC;AAChC,2BAAmC;AACnC,sBAAsB;AACtB,6BAA0B;AAC1B,oCAA+B;AAC/B,0CAAqC;AACrC,6BAA6B;AAgEnB;AA5DV,IAAM,cAAc;AACpB,IAAM,gBAAgB,EAAE,SAAS,OAAO,YAAY,KAAK;AAMzD,IAAM,aAAa;AAGnB,IAAM,CAAC,YAAY,eAAe,qBAAqB,QAAI,0CAGzD,UAAU;AAGZ,IAAM,CAAC,+BAA+B,2BAA2B,QAAI;AAAA,EACnE;AAAA,EACA,CAAC,qBAAqB;AACxB;AA8BA,IAAM,CAAC,qBAAqB,qBAAqB,IAC/C,8BAAkD,UAAU;AAK9D,IAAM,mBAAyB;AAAA,EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,WACE,4CAAC,WAAW,UAAX,EAAoB,OAAO,MAAM,yBAChC,sDAAC,WAAW,MAAX,EAAgB,OAAO,MAAM,yBAC5B,sDAAC,wBAAsB,GAAG,OAAO,KAAK,cAAc,GACtD,GACF;AAAA,EAEJ;AACF;AAEA,iBAAiB,cAAc;AAgB/B,IAAM,uBAA6B,iBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA,kBAAkB;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA,4BAA4B;AAAA,IAC5B,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,MAAY,aAAoC,IAAI;AAC1D,QAAM,mBAAe,2CAAgB,cAAc,GAAG;AACtD,QAAM,gBAAY,qCAAa,GAAG;AAClC,QAAM,CAAC,kBAAkB,mBAAmB,QAAI,0DAAqB;AAAA,IACnE,MAAM;AAAA,IACN,aAAa,2BAA2B;AAAA,IACxC,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,eAAS,KAAK;AACpE,QAAM,uBAAmB,8CAAe,YAAY;AACpD,QAAM,WAAW,cAAc,uBAAuB;AACtD,QAAM,kBAAwB,aAAO,KAAK;AAC1C,QAAM,CAAC,qBAAqB,sBAAsB,IAAU,eAAS,CAAC;AAEtE,EAAM,gBAAU,MAAM;AACpB,UAAM,OAAO,IAAI;AACjB,QAAI,MAAM;AACR,WAAK,iBAAiB,aAAa,gBAAgB;AACnD,aAAO,MAAM,KAAK,oBAAoB,aAAa,gBAAgB;AAAA,IACrE;AAAA,EACF,GAAG,CAAC,gBAAgB,CAAC;AAErB,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,MACP;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,aAAmB;AAAA,QACjB,CAAC,cAAc,oBAAoB,SAAS;AAAA,QAC5C,CAAC,mBAAmB;AAAA,MACtB;AAAA,MACA,gBAAsB,kBAAY,MAAM,oBAAoB,IAAI,GAAG,CAAC,CAAC;AAAA,MACrE,oBAA0B;AAAA,QACxB,MAAM,uBAAuB,CAAC,cAAc,YAAY,CAAC;AAAA,QACzD,CAAC;AAAA,MACH;AAAA,MACA,uBAA6B;AAAA,QAC3B,MAAM,uBAAuB,CAAC,cAAc,YAAY,CAAC;AAAA,QACzD,CAAC;AAAA,MACH;AAAA,MAEA;AAAA,QAAC,iCAAU;AAAA,QAAV;AAAA,UACC,UAAU,oBAAoB,wBAAwB,IAAI,KAAK;AAAA,UAC/D,oBAAkB;AAAA,UACjB,GAAG;AAAA,UACJ,KAAK;AAAA,UACL,OAAO,EAAE,SAAS,QAAQ,GAAG,MAAM,MAAM;AAAA,UACzC,iBAAa,uCAAqB,MAAM,aAAa,MAAM;AACzD,4BAAgB,UAAU;AAAA,UAC5B,CAAC;AAAA,UACD,aAAS,uCAAqB,MAAM,SAAS,CAAC,UAAU;AAKtD,kBAAM,kBAAkB,CAAC,gBAAgB;AAEzC,gBAAI,MAAM,WAAW,MAAM,iBAAiB,mBAAmB,CAAC,kBAAkB;AAChF,oBAAM,kBAAkB,IAAI,YAAY,aAAa,aAAa;AAClE,oBAAM,cAAc,cAAc,eAAe;AAEjD,kBAAI,CAAC,gBAAgB,kBAAkB;AACrC,sBAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,KAAK,SAAS;AACxD,sBAAM,aAAa,MAAM,KAAK,CAAC,SAAS,KAAK,MAAM;AACnD,sBAAM,cAAc,MAAM,KAAK,CAAC,SAAS,KAAK,OAAO,gBAAgB;AACrE,sBAAM,iBAAiB,CAAC,YAAY,aAAa,GAAG,KAAK,EAAE;AAAA,kBACzD;AAAA,gBACF;AACA,sBAAM,iBAAiB,eAAe,IAAI,CAAC,SAAS,KAAK,IAAI,OAAQ;AACrE,2BAAW,gBAAgB,yBAAyB;AAAA,cACtD;AAAA,YACF;AAEA,4BAAgB,UAAU;AAAA,UAC5B,CAAC;AAAA,UACD,YAAQ,uCAAqB,MAAM,QAAQ,MAAM,oBAAoB,KAAK,CAAC;AAAA;AAAA,MAC7E;AAAA;AAAA,EACF;AAEJ,CAAC;AAMD,IAAM,YAAY;AAalB,IAAM,uBAA6B;AAAA,EACjC,CAAC,OAA0C,iBAAiB;AAC1D,UAAM;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,MACZ,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,UAAM,aAAS,uBAAM;AACrB,UAAM,KAAK,aAAa;AACxB,UAAM,UAAU,sBAAsB,WAAW,uBAAuB;AACxE,UAAM,mBAAmB,QAAQ,qBAAqB;AACtD,UAAM,WAAW,cAAc,uBAAuB;AAEtD,UAAM,EAAE,oBAAoB,uBAAuB,iBAAiB,IAAI;AAExE,IAAM,gBAAU,MAAM;AACpB,UAAI,WAAW;AACb,2BAAmB;AACnB,eAAO,MAAM,sBAAsB;AAAA,MACrC;AAAA,IACF,GAAG,CAAC,WAAW,oBAAoB,qBAAqB,CAAC;AAEzD,WACE;AAAA,MAAC,WAAW;AAAA,MAAX;AAAA,QACC,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,UAAC,iCAAU;AAAA,UAAV;AAAA,YACC,UAAU,mBAAmB,IAAI;AAAA,YACjC,oBAAkB,QAAQ;AAAA,YACzB,GAAG;AAAA,YACJ,KAAK;AAAA,YACL,iBAAa,uCAAqB,MAAM,aAAa,CAAC,UAAU;AAG9D,kBAAI,CAAC,UAAW,OAAM,eAAe;AAAA,kBAEhC,SAAQ,YAAY,EAAE;AAAA,YAC7B,CAAC;AAAA,YACD,aAAS,uCAAqB,MAAM,SAAS,MAAM,QAAQ,YAAY,EAAE,CAAC;AAAA,YAC1E,eAAW,uCAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,kBAAI,MAAM,QAAQ,SAAS,MAAM,UAAU;AACzC,wBAAQ,eAAe;AACvB;AAAA,cACF;AAEA,kBAAI,MAAM,WAAW,MAAM,cAAe;AAE1C,oBAAM,cAAc,eAAe,OAAO,QAAQ,aAAa,QAAQ,GAAG;AAE1E,kBAAI,gBAAgB,QAAW;AAC7B,oBAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU,MAAM,SAAU;AACtE,sBAAM,eAAe;AACrB,sBAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,KAAK,SAAS;AACxD,oBAAI,iBAAiB,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,OAAQ;AAE1D,oBAAI,gBAAgB,OAAQ,gBAAe,QAAQ;AAAA,yBAC1C,gBAAgB,UAAU,gBAAgB,QAAQ;AACzD,sBAAI,gBAAgB,OAAQ,gBAAe,QAAQ;AACnD,wBAAM,eAAe,eAAe,QAAQ,MAAM,aAAa;AAC/D,mCAAiB,QAAQ,OACrB,UAAU,gBAAgB,eAAe,CAAC,IAC1C,eAAe,MAAM,eAAe,CAAC;AAAA,gBAC3C;AAMA,2BAAW,MAAM,WAAW,cAAc,CAAC;AAAA,cAC7C;AAAA,YACF,CAAC;AAAA,YAEA,iBAAO,aAAa,aACjB,SAAS,EAAE,kBAAkB,YAAY,oBAAoB,KAAK,CAAC,IACnE;AAAA;AAAA,QACN;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,qBAAqB,cAAc;AAKnC,IAAM,0BAAuD;AAAA,EAC3D,WAAW;AAAA,EAAQ,SAAS;AAAA,EAC5B,YAAY;AAAA,EAAQ,WAAW;AAAA,EAC/B,QAAQ;AAAA,EAAS,MAAM;AAAA,EACvB,UAAU;AAAA,EAAQ,KAAK;AACzB;AAEA,SAAS,qBAAqB,KAAa,KAAiB;AAC1D,MAAI,QAAQ,MAAO,QAAO;AAC1B,SAAO,QAAQ,cAAc,eAAe,QAAQ,eAAe,cAAc;AACnF;AAIA,SAAS,eAAe,OAA4B,aAA2B,KAAiB;AAC9F,QAAM,MAAM,qBAAqB,MAAM,KAAK,GAAG;AAC/C,MAAI,gBAAgB,cAAc,CAAC,aAAa,YAAY,EAAE,SAAS,GAAG,EAAG,QAAO;AACpF,MAAI,gBAAgB,gBAAgB,CAAC,WAAW,WAAW,EAAE,SAAS,GAAG,EAAG,QAAO;AACnF,SAAO,wBAAwB,GAAG;AACpC;AAEA,SAAS,WAAW,YAA2B,gBAAgB,OAAO;AACpE,QAAM,6BAA6B,SAAS;AAC5C,aAAW,aAAa,YAAY;AAElC,QAAI,cAAc,2BAA4B;AAC9C,cAAU,MAAM,EAAE,cAAc,CAAC;AACjC,QAAI,SAAS,kBAAkB,2BAA4B;AAAA,EAC7D;AACF;AAMA,SAAS,UAAa,OAAY,YAAoB;AACpD,SAAO,MAAM,IAAO,CAAC,GAAG,UAAU,OAAO,aAAa,SAAS,MAAM,MAAM,CAAE;AAC/E;AAEA,IAAM,OAAO;AACb,IAAM,OAAO;", "names": []}