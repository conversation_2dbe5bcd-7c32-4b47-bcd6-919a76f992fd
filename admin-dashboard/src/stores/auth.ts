import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authApi, LoginRequest, RegisterRequest } from '@/lib/api';
import toast from 'react-hot-toast';

interface User {
  user_id: string;
  email: string;
  full_name: string;
  is_staff: boolean;
  email_verified: boolean;
  phone_verified: boolean;
}

interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (credentials: LoginRequest) => Promise<boolean>;
  register: (userData: RegisterRequest) => Promise<boolean>;
  logout: () => Promise<void>;
  clearError: () => void;
  checkAuth: () => Promise<void>;
  refreshAuth: () => Promise<boolean>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Login action
      login: async (credentials: LoginRequest): Promise<boolean> => {
        set({ isLoading: true, error: null });

        try {
          const response = await authApi.login(credentials);
          
          const user: User = {
            user_id: response.user_id,
            email: response.email,
            full_name: response.full_name,
            is_staff: response.is_staff,
            email_verified: response.email_verified,
            phone_verified: response.phone_verified,
          };

          // Store user data in API client
          authApi.setUserData(response);

          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          console.log('Login successful, user data set:', user);
          // إزالة رسالة الترحيب لتجنب ظهورها
          // toast.success(`مرحباً ${user.full_name}!`);
          
          // إزالة التأخير لجعل العملية أسرع
          // await new Promise(resolve => setTimeout(resolve, 100));
          
          return true;
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'فشل في تسجيل الدخول';
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });

          console.error('Login failed:', errorMessage);
          toast.error(errorMessage);
          return false;
        }
      },

      // Register action
      register: async (userData: RegisterRequest): Promise<boolean> => {
        set({ isLoading: true, error: null });

        try {
          await authApi.register(userData);
          
          set({
            isLoading: false,
            error: null,
          });

          toast.success('تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني.');
          return true;
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'فشل في إنشاء الحساب';
          set({
            isLoading: false,
            error: errorMessage,
          });

          toast.error(errorMessage);
          return false;
        }
      },

      // Logout action
      logout: async (): Promise<void> => {
        set({ isLoading: true });

        try {
          await authApi.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });

          toast.success('تم تسجيل الخروج بنجاح');
        }
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },

      // Check authentication status
      checkAuth: async (): Promise<void> => {
        const isAuth = authApi.isAuthenticated();
        const userData = authApi.getUserData();

        if (isAuth && userData) {
          const user: User = {
            user_id: userData.user_id || '',
            email: userData.email || '',
            full_name: userData.full_name || '',
            is_staff: userData.is_staff || false,
            email_verified: userData.email_verified || false,
            phone_verified: userData.phone_verified || false,
          };

          set({
            user,
            isAuthenticated: true,
          });
        } else {
          set({
            user: null,
            isAuthenticated: false,
          });
        }
      },

      // Refresh authentication
      refreshAuth: async (): Promise<boolean> => {
        try {
          const response = await authApi.refreshToken();
          
          const user: User = {
            user_id: response.user_id,
            email: response.email,
            full_name: response.full_name,
            is_staff: response.is_staff,
            email_verified: response.email_verified,
            phone_verified: response.phone_verified,
          };

          authApi.setUserData(response);

          set({
            user,
            isAuthenticated: true,
            error: null,
          });

          return true;
        } catch {
          // If refresh fails, logout user
          get().logout();
          return false;
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Helper hooks
export const useAuth = () => {
  const store = useAuthStore();
  return {
    user: store.user,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    error: store.error,
    isAdmin: store.user?.is_staff || false,
    login: store.login,
    register: store.register,
    logout: store.logout,
    clearError: store.clearError,
    checkAuth: store.checkAuth,
    refreshAuth: store.refreshAuth,
  };
};
