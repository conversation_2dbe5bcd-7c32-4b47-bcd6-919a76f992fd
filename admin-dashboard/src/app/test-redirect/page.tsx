'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';

export default function TestRedirectPage() {
  const testRedirect = () => {
    console.log('Testing redirect...');
    window.location.href = '/admin/dashboard';
  };

  const testRouterPush = () => {
    console.log('Testing router push...');
    window.location.assign('/admin/dashboard');
  };

  const testRouterReplace = () => {
    console.log('Testing router replace...');
    window.location.replace('/admin/dashboard');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-lg space-y-4">
        <h1 className="text-2xl font-bold text-center">اختبار التوجيه</h1>
        
        <div className="space-y-4">
          <Button onClick={testRedirect} className="w-full">
            اختبار window.location.href
          </Button>
          
          <Button onClick={testRouterPush} className="w-full" variant="outline">
            اختبار window.location.assign
          </Button>
          
          <Button onClick={testRouterReplace} className="w-full" variant="secondary">
            اختبار window.location.replace
          </Button>
        </div>
        
        <div className="text-center text-sm text-gray-600">
          <p>اضغط على أي زر لاختبار التوجيه إلى لوحة التحكم</p>
        </div>
      </div>
    </div>
  );
}
