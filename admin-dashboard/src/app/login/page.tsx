'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import Link from 'next/link';
import { Eye, EyeOff, Lock, Mail, LogIn, Shield, Building2, CheckCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

import { useAuth } from '@/stores/auth';
import { LoginRequest } from '@/lib/api';

// Validation schema
const loginSchema = z.object({
  email: z
    .string()
    .min(1, 'البريد الإلكتروني مطلوب')
    .email('يرجى إدخال بريد إلكتروني صحيح'),
  password: z
    .string()
    .min(1, 'كلمة المرور مطلوبة')
    .min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const router = useRouter();
  const { login, isLoading, error, isAuthenticated, clearError } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [loginSuccess, setLoginSuccess] = useState(false);

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      console.log('User is authenticated, redirecting to dashboard...');
      window.location.href = '/admin/dashboard';
    }
  }, [isAuthenticated]);

  // Clear error when component unmounts or form changes
  useEffect(() => {
    return () => clearError();
  }, [clearError]);

  const onSubmit = async (data: LoginFormData) => {
    clearError();
    setLoginSuccess(false);

    const credentials: LoginRequest = {
      email: data.email,
      password: data.password,
    };

    try {
      const success = await login(credentials);

      if (success) {
        console.log('Login successful, redirecting immediately...');
        // الانتقال الفوري بعد نجاح تسجيل الدخول
        window.location.href = '/admin/dashboard';
      }
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6 px-4 sm:px-6 lg:px-8">
      {/* Background Pattern */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
      </div>

      <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl space-y-8 relative z-10">
        {/* Header Section */}
        <div className="text-center space-y-4">
          {/* Logo */}
          <div className="mx-auto h-20 w-20 flex items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-indigo-700 shadow-2xl">
            <Building2 className="h-10 w-10 text-white" />
          </div>
          
          {/* Title */}
          <div className="space-y-2">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900">
              لوحة التحكم الإدارية
            </h1>
            <p className="text-base sm:text-lg text-gray-600 max-w-md mx-auto">
              تسجيل الدخول للوصول إلى لوحة التحكم الإدارية
            </p>
          </div>
        </div>

        {/* Login Form Card */}
        <Card className="shadow-2xl border-0 bg-white/90 backdrop-blur-sm">
          <CardHeader className="space-y-3 pb-6 px-6 sm:px-8">
            <CardTitle className="text-2xl sm:text-3xl text-center text-gray-900 font-bold">
              مرحباً بك
            </CardTitle>
            <CardDescription className="text-center text-gray-600 text-base">
              يرجى إدخال بياناتك لتسجيل الدخول إلى النظام
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6 px-6 sm:px-8 pb-8">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Email Field */}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel className="flex items-center gap-2 text-gray-700 font-semibold text-base">
                        <Mail className="h-5 w-5 text-blue-600" />
                        البريد الإلكتروني
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          placeholder="أدخل بريدك الإلكتروني"
                          disabled={isLoading}
                          className="h-14 text-right border-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-all duration-200 text-base rounded-xl"
                          dir="ltr"
                        />
                      </FormControl>
                      <FormMessage className="text-sm" />
                    </FormItem>
                  )}
                />

                {/* Password Field */}
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel className="flex items-center gap-2 text-gray-700 font-semibold text-base">
                        <Lock className="h-5 w-5 text-blue-600" />
                        كلمة المرور
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            type={showPassword ? 'text' : 'password'}
                            placeholder="أدخل كلمة المرور"
                            disabled={isLoading}
                            className="h-14 pr-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-all duration-200 text-base rounded-xl"
                          />
                          <button
                            type="button"
                            onClick={togglePasswordVisibility}
                            className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                            disabled={isLoading}
                          >
                            {showPassword ? (
                              <EyeOff className="h-5 w-5" />
                            ) : (
                              <Eye className="h-5 w-5" />
                            )}
                          </button>
                        </div>
                      </FormControl>
                      <FormMessage className="text-sm" />
                    </FormItem>
                  )}
                />

                {/* Error Message */}
                {error && (
                  <div className="bg-red-50 border-2 border-red-200 rounded-xl p-4 animate-in slide-in-from-top-2">
                    <div className="flex items-center gap-3">
                      <Shield className="h-5 w-5 text-red-500 flex-shrink-0" />
                      <p className="text-red-700 font-medium">{error}</p>
                    </div>
                  </div>
                )}

                {/* Submit Button */}
                <Button
                  type="submit"
                  className="w-full h-14 bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-200 rounded-xl"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center gap-3">
                      <div className="h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      <span>جاري تسجيل الدخول...</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-3">
                      <LogIn className="h-5 w-5" />
                      <span>تسجيل الدخول</span>
                    </div>
                  )}
                </Button>

                {/* زر اختبار التوجيه */}
                <Button
                  type="button"
                  onClick={() => {
                    console.log('Test redirect button clicked');
                    window.location.href = '/admin/dashboard';
                  }}
                  variant="outline"
                  className="w-full"
                >
                  اختبار التوجيه مباشرة
                </Button>

                {/* Demo Credentials */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl p-5">
                  <div className="flex items-center gap-2 mb-3">
                    <CheckCircle className="h-5 w-5 text-blue-600" />
                    <p className="text-blue-800 font-semibold text-base">
                      بيانات تجريبية للاختبار
                    </p>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-blue-700">البريد الإلكتروني:</span>
                      <code className="bg-blue-100 px-2 py-1 rounded text-blue-800 font-mono"><EMAIL></code>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-blue-700">كلمة المرور:</span>
                      <code className="bg-blue-100 px-2 py-1 rounded text-blue-800 font-mono">admin123</code>
                    </div>
                  </div>
                </div>

                {/* Links */}
                <div className="text-center space-y-4 pt-4">
                  <Link
                    href="/forgot-password"
                    className="text-blue-600 hover:text-blue-700 font-semibold text-base transition-colors block"
                  >
                    نسيت كلمة المرور؟
                  </Link>
                  <div className="text-gray-600 text-base">
                    ليس لديك حساب؟{' '}
                    <Link
                      href="/register"
                      className="text-blue-600 hover:text-blue-700 font-semibold transition-colors"
                    >
                      إنشاء حساب جديد
                    </Link>
                  </div>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center">
          <p className="text-gray-500 text-sm">
            © 2025 لوحة التحكم الإدارية. جميع الحقوق محفوظة.
          </p>
        </div>
      </div>

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes blob {
          0% {
            transform: translate(0px, 0px) scale(1);
          }
          33% {
            transform: translate(30px, -50px) scale(1.1);
          }
          66% {
            transform: translate(-20px, 20px) scale(0.9);
          }
          100% {
            transform: translate(0px, 0px) scale(1);
          }
        }
        .animate-blob {
          animation: blob 7s infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>
    </div>
  );
}
