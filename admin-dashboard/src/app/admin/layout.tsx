import { Metadata } from 'next';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { Sidebar } from '@/components/admin/sidebar';

export const metadata: Metadata = {
  title: 'لوحة التحكم الإدارية',
  description: 'لوحة تحكم إدارية لإدارة المتجر الإلكتروني',
};

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ProtectedRoute requireAdmin={true}>
      <div className="min-h-screen bg-gray-50">
        <div className="flex">
          {/* Sidebar */}
          <Sidebar />

          {/* Main content */}
          <main className="flex-1 overflow-auto">
            <div className="p-3 sm:p-4 lg:p-6">
              {children}
            </div>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
