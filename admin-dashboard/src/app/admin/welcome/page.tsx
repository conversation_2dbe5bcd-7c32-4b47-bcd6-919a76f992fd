'use client';

import { useAuth } from '@/stores/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Crown,
  Sparkles,
  Calendar,
  Clock,
  ArrowRight,
  CheckCircle,
  Star,
  Gift,
  Zap,
  Heart,
  Trophy,
  Rocket
} from 'lucide-react';
import { useEffect, useState } from 'react';
import Link from 'next/link';

export default function WelcomePage() {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showConfetti, setShowConfetti] = useState(true);
  const [countdown, setCountdown] = useState(10);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // Hide confetti after 3 seconds
    const confettiTimer = setTimeout(() => {
      setShowConfetti(false);
    }, 3000);

    // Countdown timer
    const countdownTimer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          // Auto redirect to dashboard after countdown
          window.location.href = '/admin/dashboard';
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearInterval(timer);
      clearTimeout(confettiTimer);
      clearInterval(countdownTimer);
    };
  }, []);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'صباح الخير';
    if (hour < 17) return 'مساء الخير';
    return 'مساء الخير';
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('ar-SA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const features = [
    {
      icon: Trophy,
      title: 'إدارة شاملة',
      description: 'تحكم كامل في جميع جوانب متجرك الإلكتروني'
    },
    {
      icon: Zap,
      title: 'أداء سريع',
      description: 'واجهة سريعة ومتجاوبة لتجربة مستخدم مثالية'
    },
    {
      icon: Heart,
      title: 'سهولة الاستخدام',
      description: 'تصميم بديهي يجعل الإدارة أمراً ممتعاً'
    },
    {
      icon: Rocket,
      title: 'تحديثات مستمرة',
      description: 'ميزات جديدة وتحسينات دورية للنظام'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 relative overflow-hidden">
      {/* Animated Background Elements */}
      {showConfetti && (
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className={`absolute w-2 h-2 bg-gradient-to-r ${
                i % 4 === 0 ? 'from-yellow-400 to-orange-500' :
                i % 4 === 1 ? 'from-blue-400 to-purple-500' :
                i % 4 === 2 ? 'from-green-400 to-teal-500' :
                'from-pink-400 to-red-500'
              } rounded-full animate-bounce`}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 2}s`,
                animationDuration: `${2 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>
      )}

      <div className="container mx-auto px-4 py-8 relative z-10">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Main Welcome Card */}
          <Card className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white border-0 shadow-2xl">
            <CardContent className="p-8 sm:p-12">
              <div className="text-center space-y-6">
                {/* Crown Icon */}
                <div className="flex justify-center">
                  <div className="relative">
                    <Crown className="h-20 w-20 text-yellow-300 animate-pulse" />
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-ping"></div>
                  </div>
                </div>

                {/* Welcome Message */}
                <div className="space-y-4">
                  <Badge variant="secondary" className="bg-white/20 text-white border-white/30 text-lg px-4 py-2">
                    🎉 مرحباً بك في لوحة التحكم
                  </Badge>
                  
                  <h1 className="text-4xl sm:text-6xl font-bold">
                    {getGreeting()}, {user?.full_name || 'المدير'}!
                  </h1>
                  
                  <p className="text-xl sm:text-2xl text-blue-100 max-w-2xl mx-auto">
                    نحن سعداء لرؤيتك هنا. لوحة التحكم الإدارية جاهزة لمساعدتك في إدارة متجرك بكفاءة وسهولة
                  </p>
                </div>

                {/* Time and Date */}
                <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-blue-100">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    <span className="text-lg">{formatDate(currentTime)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    <span className="text-lg font-mono">{formatTime(currentTime)}</span>
                  </div>
                </div>

                {/* Action Button */}
                <div className="pt-4 space-y-4">
                  <Link href="/admin/dashboard">
                    <Button
                      size="lg"
                      className="bg-white text-blue-600 hover:bg-blue-50 text-lg px-8 py-4 shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <span>ابدأ الإدارة الآن</span>
                      <ArrowRight className="mr-2 h-5 w-5" />
                    </Button>
                  </Link>

                  {/* Auto redirect countdown */}
                  <div className="text-blue-100 text-sm">
                    <p>سيتم التوجيه تلقائياً إلى لوحة التحكم خلال {countdown} ثانية</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Features Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <Card key={index} className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg">
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-lg font-bold text-gray-900">{feature.title}</h3>
                        <p className="text-gray-600">{feature.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Success Message */}
          <Card className="bg-green-50 border-green-200 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-100 rounded-full">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <div className="space-y-1">
                  <h3 className="text-lg font-bold text-green-800">تم تسجيل الدخول بنجاح!</h3>
                  <p className="text-green-700">
                    تم تسجيل دخولك بنجاح إلى لوحة التحكم الإدارية. يمكنك الآن الوصول إلى جميع الميزات والأدوات المتاحة.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Links */}
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-500" />
                روابط سريعة
              </CardTitle>
              <CardDescription>
                الوصول السريع للصفحات الأساسية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <Link href="/admin/dashboard">
                  <Button variant="outline" className="w-full h-16 flex flex-col gap-2 hover:bg-blue-50 hover:border-blue-300">
                    <Sparkles className="h-5 w-5 text-blue-500" />
                    <span>لوحة التحكم</span>
                  </Button>
                </Link>
                <Link href="/components-demo">
                  <Button variant="outline" className="w-full h-16 flex flex-col gap-2 hover:bg-purple-50 hover:border-purple-300">
                    <Gift className="h-5 w-5 text-purple-500" />
                    <span>معرض المكونات</span>
                  </Button>
                </Link>
                <Link href="/admin/dashboard">
                  <Button variant="outline" className="w-full h-16 flex flex-col gap-2 hover:bg-green-50 hover:border-green-300">
                    <Trophy className="h-5 w-5 text-green-500" />
                    <span>الإعدادات</span>
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
