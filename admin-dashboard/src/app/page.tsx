'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/stores/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, Users, BarChart3, Settings } from 'lucide-react';

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, isLoading, checkAuth } = useAuth();

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push('/admin/dashboard');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-blue-100 mb-6">
            <Shield className="h-8 w-8 text-blue-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            لوحة التحكم الإدارية
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            نظام إدارة متقدم للمتجر الإلكتروني
          </p>
          <div className="flex gap-4 justify-center">
            <Button
              onClick={() => router.push('/login')}
              size="lg"
              className="px-8"
            >
              تسجيل الدخول
            </Button>
            <Button
              onClick={() => router.push('/register')}
              variant="outline"
              size="lg"
              className="px-8"
            >
              إنشاء حساب
            </Button>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100 mb-4">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle>إدارة المستخدمين</CardTitle>
              <CardDescription>
                إدارة شاملة لحسابات المستخدمين والصلاحيات
              </CardDescription>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-purple-100 mb-4">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
              <CardTitle>التقارير والإحصائيات</CardTitle>
              <CardDescription>
                تقارير مفصلة وإحصائيات شاملة للأداء
              </CardDescription>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-orange-100 mb-4">
                <Settings className="h-6 w-6 text-orange-600" />
              </div>
              <CardTitle>إعدادات النظام</CardTitle>
              <CardDescription>
                تخصيص وإعداد جميع جوانب النظام
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        <div className="text-center mt-16">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>مميزات النظام</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-600">
                <div className="text-right">
                  <ul className="space-y-2">
                    <li>• واجهة مستخدم حديثة وسهلة الاستخدام</li>
                    <li>• نظام أمان متقدم مع تشفير البيانات</li>
                    <li>• تقارير وإحصائيات في الوقت الفعلي</li>
                  </ul>
                </div>
                <div className="text-right">
                  <ul className="space-y-2">
                    <li>• دعم كامل للغة العربية</li>
                    <li>• تصميم متجاوب لجميع الأجهزة</li>
                    <li>• نظام إشعارات ذكي</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="text-center mt-8">
          <p className="text-xs text-gray-500">
            © 2025 لوحة التحكم الإدارية. جميع الحقوق محفوظة.
          </p>
        </div>
      </div>
    </div>
  );
}
