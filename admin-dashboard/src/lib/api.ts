// API Configuration and Client
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://smart-ai-api.onrender.com/api/v1';

// Mock mode for development
const USE_MOCK_API = process.env.NODE_ENV === 'development' || !process.env.NEXT_PUBLIC_API_BASE_URL;

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  access: string;
  refresh: string;
  user_id: string;
  email: string;
  full_name: string;
  is_staff: boolean;
  email_verified: boolean;
  phone_verified: boolean;
}

export interface RegisterRequest {
  email: string;
  password: string;
  password_confirm: string;
  full_name: string;
  university_id?: string;
}

export interface RegisterResponse {
  message: string;
  user_id: string;
  email_verification_required: boolean;
  phone_verification_required: boolean;
}

export interface ApiError {
  message: string;
  errors?: Record<string, string[]>;
  status: number;
}

// Mock API responses for development
const MOCK_USERS = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    user_id: 'admin-001',
    full_name: 'مدير النظام',
    is_staff: true,
    email_verified: true,
    phone_verified: true,
  },
  {
    email: '<EMAIL>',
    password: 'user123',
    user_id: 'user-001',
    full_name: 'مستخدم تجريبي',
    is_staff: false,
    email_verified: true,
    phone_verified: false,
  }
];

// Mock API functions
const mockApi = {
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const user = MOCK_USERS.find(u => u.email === credentials.email && u.password === credentials.password);

    if (!user) {
      throw {
        message: 'بيانات تسجيل الدخول غير صحيحة',
        errors: { email: ['البريد الإلكتروني أو كلمة المرور غير صحيحة'] },
        status: 401,
      } as ApiError;
    }

    return {
      access: 'mock-access-token-' + Date.now(),
      refresh: 'mock-refresh-token-' + Date.now(),
      user_id: user.user_id,
      email: user.email,
      full_name: user.full_name,
      is_staff: user.is_staff,
      email_verified: user.email_verified,
      phone_verified: user.phone_verified,
    };
  },

  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Check if user already exists
    const existingUser = MOCK_USERS.find(u => u.email === userData.email);
    if (existingUser) {
      throw {
        message: 'المستخدم موجود بالفعل',
        errors: { email: ['هذا البريد الإلكتروني مستخدم بالفعل'] },
        status: 400,
      } as ApiError;
    }

    // Check password confirmation
    if (userData.password !== userData.password_confirm) {
      throw {
        message: 'كلمات المرور غير متطابقة',
        errors: { password_confirm: ['كلمات المرور غير متطابقة'] },
        status: 400,
      } as ApiError;
    }

    return {
      message: 'تم إنشاء الحساب بنجاح',
      user_id: 'user-' + Date.now(),
      email_verification_required: true,
      phone_verification_required: true,
    };
  },

  async refreshToken(refreshToken: string): Promise<{ access: string }> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));

    if (!refreshToken || !refreshToken.startsWith('mock-refresh-token')) {
      throw {
        message: 'رمز التحديث غير صالح',
        errors: {},
        status: 401,
      } as ApiError;
    }

    return {
      access: 'mock-access-token-' + Date.now(),
    };
  }
};

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // Add auth token if available
    const token = this.getAccessToken();
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    console.log('API Request:', { url, method: config.method || 'GET', headers: config.headers });

    try {
      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, 30000); // 30 seconds timeout

      // Add signal to config
      config.signal = controller.signal;

      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      console.log('API Response:', { status: response.status, statusText: response.statusText });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API Error:', errorData);
        throw {
          message: errorData.message || errorData.detail || `HTTP ${response.status}: ${response.statusText}`,
          errors: errorData.errors || {},
          status: response.status,
        } as ApiError;
      }

      const data = await response.json();
      console.log('API Success:', data);
      return data;
    } catch (error) {
      console.error('API Request Failed:', error);

      if (error instanceof Error) {
        // Handle different types of errors
        if (error.name === 'AbortError') {
          throw {
            message: 'انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.',
            status: 0,
          } as ApiError;
        }

        if (error.message.includes('fetch') || error.message.includes('NetworkError')) {
          throw {
            message: 'فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.',
            status: 0,
          } as ApiError;
        }

        throw {
          message: error.message,
          status: 0,
        } as ApiError;
      }
      throw error;
    }
  }

  // Authentication methods
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    // Use mock API in development mode or when API is not available
    if (USE_MOCK_API) {
      console.log('Using mock API for login');
      try {
        const response = await mockApi.login(credentials);

        // Store tokens
        this.setTokens(response.access, response.refresh);
        this.setUserData(response);

        return response;
      } catch (error) {
        console.error('Mock API login error:', error);
        throw error;
      }
    }

    try {
      const response = await this.request<LoginResponse>('/auth/login/', {
        method: 'POST',
        body: JSON.stringify(credentials),
      });

      // Store tokens
      this.setTokens(response.access, response.refresh);
      this.setUserData(response);

      return response;
    } catch (error) {
      // Fallback to mock API if real API fails
      console.log('Real API failed, falling back to mock API');
      try {
        const response = await mockApi.login(credentials);

        // Store tokens
        this.setTokens(response.access, response.refresh);
        this.setUserData(response);

        return response;
      } catch (mockError) {
        console.error('Mock API also failed:', mockError);
        throw mockError;
      }
    }
  }

  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    // Use mock API in development mode or when API is not available
    if (USE_MOCK_API) {
      console.log('Using mock API for register');
      return mockApi.register(userData);
    }

    try {
      return this.request<RegisterResponse>('/auth/register/', {
        method: 'POST',
        body: JSON.stringify(userData),
      });
    } catch (error) {
      // Fallback to mock API if real API fails
      console.log('Real API failed, falling back to mock API for register');
      return mockApi.register(userData);
    }
  }

  async logout(): Promise<void> {
    const refreshToken = this.getRefreshToken();
    if (refreshToken) {
      try {
        await this.request('/auth/logout/', {
          method: 'POST',
          body: JSON.stringify({ refresh: refreshToken }),
        });
      } catch (error) {
        console.error('Logout error:', error);
      }
    }
    
    this.clearTokens();
  }

  async refreshToken(): Promise<LoginResponse> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    // Use mock API in development mode or when API is not available
    if (USE_MOCK_API) {
      console.log('Using mock API for token refresh');
      const response = await mockApi.refreshToken(refreshToken);
      this.setTokens(response.access, refreshToken); // Keep the same refresh token

      // Return a full LoginResponse (mock doesn't return user data, so we'll use stored data)
      const userData = this.getUserData();
      return {
        access: response.access,
        refresh: refreshToken,
        user_id: userData?.user_id || 'mock-user',
        email: userData?.email || '<EMAIL>',
        full_name: userData?.full_name || 'مدير النظام',
        is_staff: userData?.is_staff || true,
        email_verified: userData?.email_verified || true,
        phone_verified: userData?.phone_verified || false,
      };
    }

    try {
      const response = await this.request<LoginResponse>('/auth/token/refresh/', {
        method: 'POST',
        body: JSON.stringify({ refresh: refreshToken }),
      });

      this.setTokens(response.access, response.refresh);
      return response;
    } catch (error) {
      // Fallback to mock API if real API fails
      console.log('Real API failed, falling back to mock API for token refresh');
      const response = await mockApi.refreshToken(refreshToken);
      this.setTokens(response.access, refreshToken);

      const userData = this.getUserData();
      return {
        access: response.access,
        refresh: refreshToken,
        user_id: userData?.user_id || 'mock-user',
        email: userData?.email || '<EMAIL>',
        full_name: userData?.full_name || 'مدير النظام',
        is_staff: userData?.is_staff || true,
        email_verified: userData?.email_verified || true,
        phone_verified: userData?.phone_verified || false,
      };
    }
  }

  async resetPassword(email: string): Promise<{ message: string }> {
    return this.request('/auth/password/reset/request/', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async confirmPasswordReset(token: string, newPassword: string): Promise<{ message: string }> {
    return this.request('/auth/password/reset/confirm/', {
      method: 'POST',
      body: JSON.stringify({ token, new_password: newPassword }),
    });
  }

  async changePassword(oldPassword: string, newPassword: string): Promise<{ message: string }> {
    return this.request('/auth/password/change/', {
      method: 'POST',
      body: JSON.stringify({ old_password: oldPassword, new_password: newPassword }),
    });
  }

  // Token management
  private setTokens(accessToken: string, refreshToken: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', accessToken);
      localStorage.setItem('refresh_token', refreshToken);
    }
  }

  private getAccessToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('access_token');
    }
    return null;
  }

  private getRefreshToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('refresh_token');
    }
    return null;
  }

  private clearTokens(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user_data');
    }
  }

  // User data management
  setUserData(userData: Partial<LoginResponse>): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('user_data', JSON.stringify(userData));
    }
  }

  getUserData(): Partial<LoginResponse> | null {
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem('user_data');
      return userData ? JSON.parse(userData) : null;
    }
    return null;
  }

  isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }

  isAdmin(): boolean {
    const userData = this.getUserData();
    return userData?.is_staff || false;
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Export individual auth functions for convenience
export const authApi = {
  login: (credentials: LoginRequest) => apiClient.login(credentials),
  register: (userData: RegisterRequest) => apiClient.register(userData),
  logout: () => apiClient.logout(),
  refreshToken: () => apiClient.refreshToken(),
  resetPassword: (email: string) => apiClient.resetPassword(email),
  confirmPasswordReset: (token: string, newPassword: string) => 
    apiClient.confirmPasswordReset(token, newPassword),
  changePassword: (oldPassword: string, newPassword: string) => 
    apiClient.changePassword(oldPassword, newPassword),
  isAuthenticated: () => apiClient.isAuthenticated(),
  isAdmin: () => apiClient.isAdmin(),
  getUserData: () => apiClient.getUserData(),
  setUserData: (userData: Partial<LoginResponse>) => apiClient.setUserData(userData),
};
